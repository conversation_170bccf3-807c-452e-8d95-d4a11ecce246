# Miscellaneous
*.class
*.lock
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
.flutter-plugins-dependencies

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/
tools/stack_decoder/dist/

# Visual Studio Code related
.vscode/
node_modules
/dist
third-party/@hippy/react/node_modules
/dist_tsc

.turbo/
packages/**/dist/
packages/**/node_modules/
packages/**/.turbo/

# Log files
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# If using pnpm, this is likely not needed
package-lock.json

buildNum.txt
pnpm-lock.yaml
