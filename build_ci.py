#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import sys
import os
import hashlib
import json
import time
import requests

# 用于生成source map压缩包的加密密码
sourceMapPassword = 'r5Y!xqT4aq%TK#yh'
outputDir = 'dist'
timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime(time.time()))

env_dist = os.environ

buildNum = str(int(time.time()/60))
buildEnv = env_dist['BUILD_ENV']
tagName = env_dist['TAG_NAME']
envExport = 'export BUILD_NUMBER=' + buildNum + " && export BUILD_ENV=" + buildEnv

deployRegion = env_dist.get('DEPLOY_REGION')
if deployRegion is None:
    deployRegion = ""

if deployRegion == "" or deployRegion == "default":
    deployRegion = ""
else:
    deployRegion = deployRegion

buildOS = env_dist.get('BUILD_OS')
buildIOS = False
buildIOS9 = False
buildAndroid = False
buildOhos = False
if buildOS is None:
    buildIOS = True
    buildIOS9 = True
    buildAndroid = True
    buildOhos = True
elif buildIOS == 'IOS':
    buildIOS = True
elif buildIOS == 'Android':
    buildAndroid = True
elif buildIOS == 'IOS9':
    buildIOS9 = True
elif buildIOS == 'Ohos':
    buildOhos = True

apiUrlPrefix = ""
if buildEnv == "dev":
    apiUrlPrefix = "dev.rpc.abczs.cn"
elif buildEnv == "test":
    apiUrlPrefix = "test.rpc.abczs.cn"
elif buildEnv == "prod" or buildEnv == "gray" or buildEnv == "pre":
    apiUrlPrefix = "pre.rpc.abczs.cn"

apiUrlPrefix = "http://" + apiUrlPrefix + "/rpc/mobile/plugin/updatePlugin"


print("apiUrlPrefix = " + apiUrlPrefix)
print("deployRegion = " + deployRegion)
print("buildNum =" + buildNum)
print("tagName =" + tagName)


class PluginInfo:
    def __init__(self):
        self.name = ''
        self.version = ''
        self.md5 = ''
        self.name = ''
        self.host_min_version = ''
        self.host_max_version = ''
        self.url = ''
        self.region = ''


class PackageInfo:
    def __init__(self):
        self.hostMinVersion = ''
        self.hostMaxVersion = ''
        self.version = ''
        self.name = ''
        self.targetPlatform = ''
        self.osVersion = ''

    def loadFromJson(self, packageJson):
        self.hostMinVersion = packageJson['hostMinVersion']
        self.hostMaxVersion = packageJson['hostMaxVersion']
        self.version = packageJson['version']
        self.name = packageJson['name']



def _loadPackageInfo():
    with open("./package.json", "r") as load_f:
        packageJson = json.load(load_f)
        packageInfo = PackageInfo()
        packageInfo.loadFromJson(packageJson)
        return packageInfo

def _build_platform():
    if buildIOS:
        os.system('rsync -av --exclude=\'*.map\' ./dist/global/ ./dist/ios')
        os.system('mv ./dist/ios/index.global.js ./dist/ios/index.ios.js')
        os.system('mv ./dist/ios/vendor.global.js ./dist/ios/vendor.ios.js')

    if buildIOS9:
        os.system('rsync -av --exclude=\'*.map\' ./dist/global/ ./dist/ios-9.0')
        os.system('mv ./dist/ios-9.0/index.global.js ./dist/ios-9.0/index.ios.js')
        os.system('mv ./dist/ios-9.0/vendor.global.js ./dist/ios-9.0/vendor.ios.js')

    if buildAndroid:
        os.system('rsync -av --exclude=\'*.map\' ./dist/global/ ./dist/android')
        os.system('mv ./dist/android/index.global.js ./dist/android/index.android.js')
        os.system('mv ./dist/android/vendor.global.js ./dist/android/vendor.android.js')

def _build_platform_ohos():
    if buildOhos:
        os.system('rsync -av --exclude=\'*.map\' ./dist/global/ ./dist/ohos')
        os.system('mv ./dist/ohos/index.global.js ./dist/ohos/index.ohos.js')
        os.system('mv ./dist/ohos/vendor.global.js ./dist/ohos/vendor.ohos.js')

def _build_global():
    initDirsCmd = 'rm -rf {0}'.format(outputDir)
    os.system(initDirsCmd)

    print('删除node_modules目录')
    rmNodeModules = 'rm -rf node_modules'
    os.system(rmNodeModules)

    print('修改package.json中的@hippy/react路径')
    with open("./package.json", "r") as f:
        package_data = json.load(f)
        package_data['dependencies']['@hippy/react'] = "file:third-party/@hippy/react_2.x"
    with open("./package.json", "w") as f:
        json.dump(package_data, f, indent=2, ensure_ascii=False)

    print('node version:')
    os.system('node --version')
    print('pnpm version:')
    os.system('pnpm --version')
    buildVendorCmd = 'pnpm install --no-frozen-lockfile'
    if os.system(buildVendorCmd) != 0:
        print('pnpm i 失败')
        raise Exception("pnpm i 失败")

    buildMonoRepoCmd = 'pnpm run build'
    if os.system(buildMonoRepoCmd) != 0:
        print('pnpm run build 失败')
        raise Exception("pnpm run build 失败")

    # buildVendorCmd = 'pnpm run tsc'

    # if os.system(buildVendorCmd) != 0:
    #     print('pnpm run tsc')
    #     raise Exception("pnpm run tsc 语法检测失败")

    buildVendorCmd = 'pnpm run hippy:vendor'
    if os.system(buildVendorCmd) != 0:
        print('build hippy:vendor失败')
        raise Exception("build hippy:vendor失败")

    buildCmd = 'pnpm run hippy:build'
    if os.system(buildCmd) != 0:
        print('build hippy:build失败')
        raise Exception("build hippy:build失败")

def _build_ohos():
    
    print('删除node_modules目录')
    rmNodeModules = 'rm -rf node_modules'
    os.system(rmNodeModules)

    print('修改package.json中的@hippy/react路径')
    with open("./package.json", "r") as f:
        package_data = json.load(f)
        package_data['dependencies']['@hippy/react'] = "file:third-party/@hippy/react"
    with open("./package.json", "w") as f:
        json.dump(package_data, f, indent=2, ensure_ascii=False)

    print('node version:')
    os.system('node --version')
    print('pnpm version:')
    os.system('pnpm --version')
    buildVendorCmd = 'pnpm install --no-frozen-lockfile'
    if os.system(buildVendorCmd) != 0:
        print('pnpm i 失败')
        raise Exception("pnpm i 失败")

    # buildVendorCmd = 'pnpm run tsc'

    # if os.system(buildVendorCmd) != 0:
    #     print('pnpm run tsc')
    #     raise Exception("pnpm run tsc 语法检测失败")

    buildVendorCmd = 'pnpm run hippy:vendor'
    if os.system(buildVendorCmd) != 0:
        print('build hippy:vendor失败')
        raise Exception("build hippy:vendor失败")

    buildCmd = 'pnpm run hippy:build'
    if os.system(buildCmd) != 0:
        print('build hippy:build失败')
        raise Exception("build hippy:build失败")

def _ossUpload():
    buildCmd = envExport + ' && pnpm run ossZipUpload'
    print("_ossUpload cmd = " + buildCmd)
    if os.system(buildCmd) != 0:
        print('ossZipUpload 失败')
        raise Exception("OSS 上传失败")


def _fileMD5(file):
    f = open(file, 'rb')
    md5_obj = hashlib.md5()
    while True:
        d = f.read(8096)
        if not d:
            break
        md5_obj.update(d)
    hash_code = md5_obj.hexdigest()
    f.close()
    md5 = str(hash_code).lower()
    return md5


def _writeConfFile(file, packageInfo):
    with open(file, "w") as f:
        jsonConf = {}
        jsonConf['hostMinVersion'] = packageInfo.hostMinVersion
        jsonConf['hostMaxVersion'] = packageInfo.hostMaxVersion
        jsonConf['name'] = packageInfo.name
        jsonConf['version'] = packageInfo.version
        jsonConf['timestamp'] = timestamp
        jsonConf['repoTag'] = tagName
        json.dump(jsonConf, f)


def _writeZipInfo(infoFile, zipFile, packageInfo):
    pluginInfo = {}
    pluginInfo['name'] = packageInfo.name
    pluginInfo['version'] = packageInfo.version
    pluginInfo['md5'] = _fileMD5(zipFile)
    pluginInfo['hostMinVersion'] = packageInfo.hostMinVersion
    pluginInfo['hostMaxVersion'] = packageInfo.hostMaxVersion
    if deployRegion != "":
        pluginInfo['region'] = deployRegion
    pluginInfo['appId'] = "cn.abcyun.clinic.app"
    pluginInfo['targetPlatform'] = packageInfo.targetPlatform
    if packageInfo.osVersion != "":
        pluginInfo['osVersion'] = packageInfo.osVersion

    with open(infoFile, "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))


def _generateZipPlugins(packageInfo):
    zipNameSuffix = "{0}_{1}_{2}_{3}.zip".format(packageInfo.name, packageInfo.version, deployRegion, timestamp)
    if buildAndroid:
        _writeConfFile('dist/android/conf.json', packageInfo)
        cmd = 'cd dist/android && zip -q -r -o  ../android.zip .'
        if os.system(cmd) != 0:
            print('生成zip包失败')
            return False

        newAndroidZipName = outputDir + '/android_{0}'.format(zipNameSuffix)
        mvAndroidZipCmd = 'mv ' + outputDir + '/android.zip  ' + newAndroidZipName
        print("mvAndroidZipCmd = " + mvAndroidZipCmd)
        os.system(mvAndroidZipCmd)
        packageInfo.targetPlatform = '2'
        print("packageInfo.android_zip_info.targetPlatform = " + packageInfo.targetPlatform)
        _writeZipInfo("./dist/android_zip_info.txt", newAndroidZipName, packageInfo)

    if buildIOS:
        _writeConfFile('dist/ios/conf.json', packageInfo)
        cmd = 'cd dist/ios && zip -q -r -o  ../ios.zip .'
        if os.system(cmd) != 0:
            print('生成zip包失败')
            return False

        newIOSZipName = outputDir + '/ios_{0}'.format(zipNameSuffix)
        mvIOSZipCmd = 'mv ' + outputDir + '/ios.zip  ' + newIOSZipName
        os.system(mvIOSZipCmd)
        packageInfo.targetPlatform = '1'
        packageInfo.osVersion = '10.0.0-'
        print("packageInfo.ios_zip_info.targetPlatform = " + packageInfo.targetPlatform)
        print("packageInfo.ios_zip_info.osVersion = " + packageInfo.osVersion)
        _writeZipInfo("./dist/ios_zip_info.txt", newIOSZipName, packageInfo)

    if buildIOS9:
        _writeConfFile('dist/ios-9.0/conf.json', packageInfo)
        cmd = 'cd dist/ios-9.0 && zip -q -r -o  ../ios-9.0.zip .'
        if os.system(cmd) != 0:
            print('生成zip包失败')
            return False

        newIOSZipName = outputDir + '/ios_9.0_{0}'.format(zipNameSuffix)
        mvIOSZipCmd = 'mv ' + outputDir + '/ios-9.0.zip  ' + newIOSZipName
        os.system(mvIOSZipCmd)
        packageInfo.osVersion = '-9.9.9'
        print("packageInfo.0_zip_info.targetPlatform = " + packageInfo.targetPlatform)
        print("packageInfo.0_zip_info.osVersion = " + packageInfo.osVersion)
        _writeZipInfo("./dist/ios_9.0_zip_info.txt", newIOSZipName, packageInfo)

    if buildOhos:
        _writeConfFile('dist/ohos/conf.json', packageInfo)
        cmd = 'cd dist/ohos && zip -q -r -o  ../ohos.zip .'
        if os.system(cmd) != 0:
            print('生成zip包失败')
            return False

        newOhosZipName = outputDir + '/ohos_{0}'.format(zipNameSuffix)
        mvOhosZipCmd = 'mv ' + outputDir + '/ohos.zip  ' + newOhosZipName
        os.system(mvOhosZipCmd)
        packageInfo.osVersion = '-9.9.9'
        packageInfo.targetPlatform = '4'
        print("packageInfo.0_zip_info.targetPlatform = " + packageInfo.targetPlatform)
        print("packageInfo.0_zip_info.osVersion = " + packageInfo.osVersion)
        _writeZipInfo("./dist/ohos_zip_info.txt", newOhosZipName, packageInfo)


    # 清理global android ios ios-9.0 ohos
    os.system('rm -rf dist/global')
    os.system('rm -rf dist/android')
    os.system('rm -rf dist/ios')
    os.system('rm -rf dist/ios-9.0')
    os.system('rm -rf dist/ohos')

    return True

def _updateUpgradeItem(url, file, grayFlag):
    print("_updateUpgradeItem url = " + url + ", file = " + file)
    f = None
    content = None
    with open(file, 'r') as f:
        content = json.load(f)
        content['grayFlag'] = grayFlag
        content = json.dumps(content)

    print("_updateUpgradeItem url = " + url + ", content = " + content)

    headers = {"content-type": "application/json"}
    r = requests.put(url, data=content,headers=headers)
    print("_updateUpgradeItem rsp code:" + str(r.status_code))
    print("_updateUpgradeItem, rsp content = " + r.content)
    if r.status_code != 200:
        raise Exception("_updateUpgradeItem 更改配置失败")


def _updateUpgradeInfo():
    if buildEnv == "dev" or buildEnv == "test":
        if buildAndroid:
            _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "0")
            _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "1")
            _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "2")
        if buildIOS:
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "0")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "1")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "2")
        if buildIOS9:
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "0")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "1")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "2")
        if buildOhos:
            _updateUpgradeItem(apiUrlPrefix, "./dist/ohos_zip_info.txt", "0")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ohos_zip_info.txt", "1")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ohos_zip_info.txt", "2")
    # elif buildEnv == "gray":
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "1")
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "1")
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "1")
    # elif buildEnv == "prod":
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "0")
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "0")
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "0")
    # elif buildEnv == "pre":
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt", "2")
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt", "2")
    #     _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt", "2")


packageInfo = _loadPackageInfo()
packageInfo.version += '.' + buildNum

print('version = ' + packageInfo.version)


def main():
    reload(sys)
    sys.setdefaultencoding('utf8')
    _build_global()
    _build_platform()

    _build_ohos()
    _build_platform_ohos()
    _generateZipPlugins(packageInfo)
    _ossUpload()
    _updateUpgradeInfo()

if __name__ == '__main__':
    main()
