#!/usr/bin/env node

/**
 * 快速开发环境选择器
 * 提供交互式菜单来选择 dev2 或 dev3 环境
 */

const readline = require('readline');
const { spawn, execSync } = require('child_process');
const path = require('path');

// 当前选中的选项索引
let selectedIndex = 0;

// 全局子进程跟踪
let currentChildProcess = null;

// 递归终止进程树的函数
function killProcessTree(pid, signal = 'SIGINT') {
    try {
        if (process.platform === 'win32') {
            // Windows 使用 taskkill
            spawn('taskkill', ['/pid', pid, '/t', '/f'], { stdio: 'ignore' });
        } else {
            // Unix-like 系统使用 ps 和 kill
            
            // 获取所有子进程
            try {
                const children = execSync(`pgrep -P ${pid}`, { encoding: 'utf8' })
                    .trim()
                    .split('\n')
                    .filter(Boolean);
                
                // 递归终止子进程
                children.forEach(childPid => {
                    killProcessTree(childPid, signal);
                });
            } catch (error) {
                // 没有子进程或命令失败，继续执行
            }
            
            // 终止当前进程
            try {
                process.kill(pid, signal);
            } catch (error) {
                // 进程可能已经不存在
            }
        }
    } catch (error) {
        console.log(`警告: 无法终止进程 ${pid}: ${error.message}`);
    }
}

// 颜色输出工具
const colors = {
    reset: '\x1b[0m',
    bright: '\x1b[1m',
    dim: '\x1b[2m',
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    magenta: '\x1b[35m',
    cyan: '\x1b[36m',
    white: '\x1b[37m',
    bgBlue: '\x1b[44m',
    bgGreen: '\x1b[42m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

// 菜单选项配置
const menuOptions = [
    {
        key: '1',
        label: 'dev2 - Hippy React 2.x + 调试 + 开发',
        description: '切换到 Hippy React 2.x 并启动调试和开发服务器',
        command: 'dev2',
        details: [
            '• 更新 @hippy/react 到 "file:third-party/@hippy/react_2.x"',
            '• 运行 pnpm install（如果依赖发生变化）',
            '• 运行 pnpm run build',
            '• 并行启动 pnpm run hippy:debug 和 pnpm run hippy:dev'
        ]
    },
    {
        key: '2',
        label: 'dev3 - Hippy React 3.x + Dev3',
        description: '切换到 Hippy React 3.x 并启动 dev3 服务器',
        command: 'dev3',
        details: [
            '• 更新 @hippy/react 到 "file:third-party/@hippy/react"',
            '• 运行 pnpm install（如果依赖发生变化）',
            '• 运行 pnpm run build',
            '• 启动 pnpm run hippy:dev3'
        ]
    }
];

// 显示欢迎信息
function showWelcome() {
    console.clear();
    log('', 'reset');
    log('🚀 快速开发环境选择器', 'bright');
    log('==========================================', 'cyan');
    log('', 'reset');
}

// 显示菜单
function showMenu() {
    log('请选择开发环境:', 'yellow');
    log('', 'reset');

    menuOptions.forEach((option, index) => {
        const isSelected = index === selectedIndex;
        const prefix = isSelected ? colors.bgBlue + colors.white : colors.cyan;
        const suffix = colors.reset;
        const arrow = isSelected ? '▶ ' : '  ';

        log(`${prefix}${arrow}${option.key}. ${option.label}${suffix}`, 'reset');
        log(`     ${option.description}`, isSelected ? 'white' : 'dim');
        log('', 'reset');
    });

    log('📖 使用 ↑↓ 方向键或 1/2 数字键导航，回车键选择', 'magenta');
    log('❌ 按 Ctrl+C 退出', 'dim');
    log('', 'reset');
}

// 显示选项详情
function showOptionDetails(option) {
    log(`\n📋 ${option.label}`, 'bright');
    log('─'.repeat(50), 'cyan');
    option.details.forEach(detail => {
        log(detail, 'white');
    });
    log('', 'reset');
}

// 执行选择的命令
function executeCommand(command) {
    log(`🎯 正在执行: npm run ${command}`, 'green');
    log('─'.repeat(50), 'cyan');
    log('', 'reset');

    currentChildProcess = spawn('npm', ['run', command], {
        stdio: 'inherit',
        shell: true,
        cwd: process.cwd(),
        detached: true  // 创建新的进程组
    });

    currentChildProcess.on('close', (code) => {
        currentChildProcess = null; // 清理子进程引用
        if (code === 0) {
            log(`\n✅ 命令执行成功！`, 'green');
        } else {
            log(`\n❌ 命令执行失败，退出码 ${code}`, 'red');
        }
        process.exit(code);
    });

    currentChildProcess.on('error', (error) => {
        currentChildProcess = null; // 清理子进程引用
        log(`\n❌ 命令执行错误: ${error.message}`, 'red');
        process.exit(1);
    });

    // 处理进程退出信号
    process.on('SIGINT', () => {
        log('\n🛑 收到 SIGINT 信号，正在终止...', 'yellow');
        if (currentChildProcess && currentChildProcess.pid) {
            log('🔄 正在终止所有相关进程...', 'yellow');
            killProcessTree(currentChildProcess.pid, 'SIGINT');
        }
        process.exit(0);
    });

    process.on('SIGTERM', () => {
        log('\n🛑 收到 SIGTERM 信号，正在终止...', 'yellow');
        if (currentChildProcess && currentChildProcess.pid) {
            log('🔄 正在终止所有相关进程...', 'yellow');
            killProcessTree(currentChildProcess.pid, 'SIGTERM');
        }
        process.exit(0);
    });
}

// 处理键盘输入
function handleKeyPress(key) {
    if (key.name === 'up') {
        // 上箭头
        selectedIndex = selectedIndex > 0 ? selectedIndex - 1 : menuOptions.length - 1;
        refreshMenu();
    } else if (key.name === 'down') {
        // 下箭头
        selectedIndex = selectedIndex < menuOptions.length - 1 ? selectedIndex + 1 : 0;
        refreshMenu();
    } else if (key.name === 'return') {
        // 回车键
        const selectedOption = menuOptions[selectedIndex];
        executeSelectedOption(selectedOption);
    } else if (key.ctrl && key.name === 'c') {
        // Ctrl+C
        if (currentChildProcess && currentChildProcess.pid) {
            log('\n🛑 正在停止运行中的服务...', 'yellow');
            log('🔄 正在终止所有相关进程...', 'yellow');
            
            // 使用进程树终止
            killProcessTree(currentChildProcess.pid, 'SIGINT');
            
            // 给进程一些时间来清理，然后强制终止
            setTimeout(() => {
                if (currentChildProcess && currentChildProcess.pid && !currentChildProcess.killed) {
                    log('🔥 强制终止残留进程...', 'red');
                    killProcessTree(currentChildProcess.pid, 'SIGKILL');
                }
                log('👋 再见！', 'yellow');
                process.exit(0);
            }, 3000); // 增加到3秒给更多清理时间
        } else {
            log('\n👋 再见！', 'yellow');
            process.exit(0);
        }
    }
}

// 刷新菜单显示
function refreshMenu() {
    // 清屏并重新显示
    console.clear();
    showWelcome();
    showCurrentStatus();
    showMenu();
}

// 执行选中的选项
function executeSelectedOption(option) {
    console.clear();
    showOptionDetails(option);

    // 直接执行，不需要确认
    log('🚀 开始执行...', 'green');
    executeCommand(option.command);
}

// 设置键盘监听
function setupKeyboardListener() {
    // 设置原始模式以捕获键盘事件
    process.stdin.setRawMode(true);
    process.stdin.resume();
    process.stdin.setEncoding('utf8');

    // 监听键盘事件
    process.stdin.on('data', (key) => {
        const keyCode = key.charCodeAt(0);

        if (key === '\u001b[A') {
            // 上箭头
            handleKeyPress({ name: 'up' });
        } else if (key === '\u001b[B') {
            // 下箭头
            handleKeyPress({ name: 'down' });
        } else if (key === '\r' || key === '\n') {
            // 回车键
            handleKeyPress({ name: 'return' });
        } else if (key === '\u0003') {
            // Ctrl+C
            handleKeyPress({ ctrl: true, name: 'c' });
        } else if (key === '1') {
            // 数字1
            selectedIndex = 0;
            refreshMenu();
        } else if (key === '2') {
            // 数字2
            selectedIndex = 1;
            refreshMenu();
        }
    });
}

// 显示主菜单
function showMainMenu() {
    showWelcome();
    showCurrentStatus();
    showMenu();
    setupKeyboardListener();
}

// 显示当前状态
function showCurrentStatus() {
    try {
        const fs = require('fs');
        const packagePath = path.join(process.cwd(), 'package.json');
        const packageData = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
        const currentHippyReact = packageData.dependencies['@hippy/react'];

        log('📊 当前状态:', 'cyan');
        log(`   @hippy/react: ${currentHippyReact}`, 'white');

        // 判断当前是哪个版本
        if (currentHippyReact.includes('react_2.x')) {
            log('   🏷️  当前使用: Hippy React 2.x', 'green');
        } else if (currentHippyReact.includes('third-party/@hippy/react')) {
            log('   🏷️  当前使用: Hippy React 3.x', 'green');
        } else {
            log('   🏷️  当前使用: 未知版本', 'yellow');
        }
        log('', 'reset');
    } catch (error) {
        log('⚠️  无法读取当前状态', 'yellow');
        log('', 'reset');
    }
}

// 主函数
function main() {
    // 检查是否在正确的目录
    const fs = require('fs');
    const packagePath = path.join(process.cwd(), 'package.json');

    if (!fs.existsSync(packagePath)) {
        log('❌ 错误: 在当前目录中未找到 package.json', 'red');
        log('   请在项目根目录下运行此命令。', 'yellow');
        process.exit(1);
    }

    // 显示主菜单
    showMainMenu();
}

// 错误处理
process.on('uncaughtException', (error) => {
    log(`\n💥 未捕获的异常: ${error.message}`, 'red');
    process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
    log(`\n💥 未处理的 Promise 拒绝: ${promise}, 原因: ${reason}`, 'red');
    process.exit(1);
});

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    main,
    showMenu,
    executeCommand
};
