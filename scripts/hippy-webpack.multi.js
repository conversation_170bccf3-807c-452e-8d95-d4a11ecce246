const path = require('path');
const webpack = require('webpack');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
const pkg = require('../package.json');
const CopyPlugin = require('copy-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');

// 获取构建环境变量，决定构建哪些平台
const BUILD_PLATFORMS = process.env.BUILD_PLATFORMS || 'android,ios,ios9';
const platforms = BUILD_PLATFORMS.split(',');

// 检查是否构建特定平台
const shouldBuildAndroid = platforms.includes('android');
const shouldBuildIOS = platforms.includes('ios');
const shouldBuildIOS9 = platforms.includes('ios9');

// 创建基础配置
const createBaseConfig = (platform, outputPath, iosVersion = null) => {
    // 根据平台获取对应的 manifest 文件
    let manifestPath;
    if (platform === 'android') {
        manifestPath = path.resolve('./dist/android/vendor-manifest.json');
    } else {
        // iOS 和 iOS 9.0 共用同一个 vendor
        manifestPath = path.resolve('./dist/ios/vendor-manifest.json');
    }

    // 尝试加载 manifest 文件
    let manifest;
    try {
        // eslint-disable-next-line import/no-dynamic-require
        manifest = require(manifestPath);
    } catch (e) {
        console.warn(`无法加载 ${manifestPath}，请确保先执行 vendor 构建`);
        manifest = {};
    }

    // Babel 配置
    const babelOptions = {
        presets: [
            '@babel/preset-typescript',
            '@babel/preset-react',
        ],
        plugins: [
            ["@babel/plugin-proposal-decorators", {"legacy": true}],
            "@babel/plugin-proposal-optional-chaining",
            "@babel/plugin-proposal-nullish-coalescing-operator",
            "@babel/proposal-class-properties",
            "@babel/plugin-proposal-object-rest-spread",
        ]
    };

    // 如果是 iOS 平台，添加 iOS 特定的 preset-env 配置
    if (platform === 'ios' && iosVersion) {
        babelOptions.presets.push([
            '@babel/preset-env',
            {
                targets: {
                    ios: iosVersion,
                },
            },
        ]);
    }

    // 创建配置对象
    return {
        name: outputPath.replace(/^\.\/dist\//, ''), // 配置名称，用于日志输出
        mode: 'production',
        devtool: 'source-map',
        bail: true,
        entry: {
            index: ['regenerator-runtime', path.resolve(pkg.main)],
        },
        output: {
            filename: `[name].${platform}.js`,
            path: path.resolve(outputPath),
            globalObject: '(0, eval)("this")',
        },
        plugins: [
            new webpack.NamedModulesPlugin(),
            new webpack.DefinePlugin({
                'process.env.NODE_ENV': JSON.stringify('production'),
                __PLATFORM__: JSON.stringify(platform),
            }),
            new CaseSensitivePathsPlugin(),
            new webpack.DllReferencePlugin({
                context: process.cwd(),
                manifest,
            }),
            new CopyPlugin([
                {from: 'src/assets', to: './assets/'},
            ]),
        ],
        optimization: {
            minimize: platform === 'android', // 仅对 Android 启用压缩
            minimizer: platform === 'android' ? [
                new TerserPlugin({
                    test: /\.js(\?.*)?$/i,
                    parallel: 4,
                    terserOptions: {
                        keep_classnames: /Bloc$|^_Event/,
                    },
                    extractComments: false,
                })
            ] : [],
        },
        module: {
            rules: [
                // TypeScript 处理
                {
                    test: /\.tsx?$/,
                    use: [
                        {
                            loader: "babel-loader",
                            options: babelOptions,
                        },
                        'unicode-loader',
                    ]
                },
                // JSX 处理 (仅 iOS 需要特殊处理)
                ...(platform === 'ios' ? [{
                    test: /\.(jsx?)$/,
                    use: [
                        {
                            loader: 'babel-loader',
                            options: {
                                presets: [
                                    '@babel/preset-react',
                                    [
                                        '@babel/preset-env',
                                        {
                                            targets: {
                                                ios: iosVersion,
                                            },
                                        },
                                    ],
                                ],
                                plugins: [
                                    '@babel/plugin-proposal-class-properties',
                                ],
                            },
                        },
                        'unicode-loader',
                    ],
                }] : []),
                // 图片资源处理
                {
                    test: /\.(png|jpg|gif)$/,
                    use: [{
                        loader: 'file-loader',
                        options: {
                            name: '[name].[ext]',
                            outputPath: 'assets/',
                        },
                    }],
                },
                // Source map 处理
                {enforce: "pre", test: /\.js$/, loader: "source-map-loader"}
            ],
        },
        resolve: {
            extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
            modules: [path.resolve(__dirname, '../node_modules')],
            alias: {
                '@hippy/react': path.resolve(__dirname, '../node_modules/@hippy/react'),
            },
        },
    };
};

// 创建各平台配置
const configs = [];

// Android 配置
if (shouldBuildAndroid) {
    configs.push(createBaseConfig('android', './dist/android/'));
}

// iOS 配置
if (shouldBuildIOS) {
    configs.push(createBaseConfig('ios', './dist/ios/', 10));
}

// iOS 9.0 配置
if (shouldBuildIOS9) {
    configs.push(createBaseConfig('ios', './dist/ios-9.0/', 9));
}

module.exports = configs;
