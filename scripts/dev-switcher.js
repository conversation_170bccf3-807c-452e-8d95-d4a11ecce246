#!/usr/bin/env node

/**
 * 开发环境切换脚本
 * 用于在不同的Hippy React版本之间切换并启动相应的开发环境
 */

const fs = require("fs");
const path = require("path");
const { spawn, exec } = require("child_process");
const os = require("os");

// 配置常量
const PACKAGE_JSON_PATH = path.join(process.cwd(), "package.json");
const HIPPY_REACT_2X = "file:third-party/@hippy/react_2.x";
const HIPPY_REACT_3X = "file:third-party/@hippy/react";

// 颜色输出工具
const colors = {
    reset: "\x1b[0m",
    bright: "\x1b[1m",
    red: "\x1b[31m",
    green: "\x1b[32m",
    yellow: "\x1b[33m",
    blue: "\x1b[34m",
    magenta: "\x1b[35m",
    cyan: "\x1b[36m",
};

function log(message, color = "reset") {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logStep(step, message) {
    log(`[${step}] ${message}`, "cyan");
}

function logSuccess(message) {
    log(`✅ ${message}`, "green");
}

function logError(message) {
    log(`❌ ${message}`, "red");
}

function logWarning(message) {
    log(`⚠️  ${message}`, "yellow");
}

// 读取package.json
function readPackageJson() {
    try {
        const content = fs.readFileSync(PACKAGE_JSON_PATH, "utf8");
        return JSON.parse(content);
    } catch (error) {
        logError(`读取 package.json 失败: ${error.message}`);
        process.exit(1);
    }
}

// 写入package.json
function writePackageJson(packageData) {
    try {
        const content = JSON.stringify(packageData, null, 2) + "\n";
        fs.writeFileSync(PACKAGE_JSON_PATH, content, "utf8");
        logSuccess("package.json 更新成功");
    } catch (error) {
        logError(`写入 package.json 失败: ${error.message}`);
        process.exit(1);
    }
}

// 更新Hippy React依赖
function updateHippyReactDependency(version) {
    logStep("1", `正在更新 @hippy/react 依赖到 ${version}`);

    const packageData = readPackageJson();

    if (!packageData.dependencies || !packageData.dependencies["@hippy/react"]) {
        logError("在 package.json 中未找到 @hippy/react 依赖");
        process.exit(1);
    }

    const currentVersion = packageData.dependencies["@hippy/react"];
    if (currentVersion === version) {
        logWarning(`@hippy/react 已经是 ${version} 版本`);
        return true;
    }

    packageData.dependencies["@hippy/react"] = version;
    writePackageJson(packageData);

    logSuccess(`已将 @hippy/react 从 ${currentVersion} 更新到 ${version}`);
    return true;
}

// 执行命令
function executeCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
        logStep("执行", `正在执行: ${command} ${args.join(" ")}`);

        const child = spawn(command, args, {
            stdio: "inherit",
            shell: true,
            ...options,
        });

        child.on("close", (code) => {
            if (code === 0) {
                logSuccess(`命令执行完成: ${command} ${args.join(" ")}`);
                resolve(code);
            } else {
                logError(`命令执行失败，退出码 ${code}: ${command} ${args.join(" ")}`);
                reject(new Error(`命令执行失败，退出码 ${code}`));
            }
        });

        child.on("error", (error) => {
            logError(`命令执行错误: ${error.message}`);
            reject(error);
        });
    });
}

// 并行执行多个命令
async function executeParallelCommands(commands) {
    logStep("并行", `正在启动 ${commands.length} 个命令`);

    if (commands.length === 0) {
        logWarning("没有命令需要执行");
        return;
    }

    const processes = [];
    const currentWindowProcesses = []; // 新增：只跟踪当前窗口的进程

    // 第一个命令在当前窗口执行
    if (commands.length > 0) {
        const { command, args, name } = commands[0];
        log(`正在当前窗口启动 ${name}...`, "magenta");

        const child = spawn(command, args, {
            stdio: "inherit",
            shell: true,
        });

        child.on("close", (code) => {
            if (code === 0) {
                logSuccess(`${name} 执行完成`);
            } else {
                logError(`${name} 执行失败，退出码 ${code}`);
            }
        });

        child.on("error", (error) => {
            logError(`${name} 执行错误: ${error.message}`);
        });

        processes.push({ child, name });
        currentWindowProcesses.push({ child, name }); // 添加到当前窗口进程列表
    }

    // 其余命令在新窗口执行 (仅针对 macOS)
    if (commands.length > 1 && process.platform === "darwin") {
        const remainingCommands = commands.slice(1);

        remainingCommands.forEach(({ command, args, name }, index) => {
            log(`正在为 ${name} 启动新窗口...`, "magenta");

            const projectDir = process.cwd();
            const scriptContent = `#!/bin/bash
# 修复 nvm 环境变量冲突
unset npm_config_prefix

# 切换到项目目录
cd "${projectDir}"
echo "启动 ${name}"
echo "工作目录: $(pwd)"
echo "执行命令: ${command} ${args.join(" ")}"
echo "================================"

# 执行命令
${command} ${args.join(" ")}

echo ""
echo "命令执行完成，按任意键关闭窗口..."
read -n 1

# 清理临时脚本文件
rm -f "$0"
`;

            // 创建临时脚本文件
            const tempDir = os.tmpdir();
            const scriptPath = path.join(tempDir, `dev_command_${index + 1}_${Date.now()}.sh`);

            try {
                fs.writeFileSync(scriptPath, scriptContent);
                fs.chmodSync(scriptPath, "755");

                // 使用 open 命令启动新的 Terminal 窗口
                const child = spawn("open", ["-a", "Terminal", scriptPath], {
                    stdio: "pipe",
                    cwd: process.cwd(),
                    detached: true,
                });

                child.on("close", (code) => {
                    if (code === 0) {
                        logSuccess(`${name} 窗口启动完成`);
                    } else {
                        logError(`${name} 窗口启动失败，退出码 ${code}`);
                    }
                });

                child.on("error", (error) => {
                    logError(`${name} 窗口启动错误: ${error.message}`);
                });

                // 分离子进程
                if (child.pid) {
                    child.unref();
                }

                processes.push({ child, name });

                // 不再提前删除临时文件，让脚本自己清理
            } catch (error) {
                logError(`创建临时脚本失败: ${error.message}`);
                // 回退到当前终端执行
                const fallbackChild = spawn(command, args, {
                    stdio: "inherit",
                    shell: true,
                });

                fallbackChild.on("close", (code) => {
                    if (code === 0) {
                        logSuccess(`${name} 执行完成`);
                    } else {
                        logError(`${name} 执行失败，退出码 ${code}`);
                    }
                });

                fallbackChild.on("error", (error) => {
                    logError(`${name} 执行错误: ${error.message}`);
                });

                processes.push({ child: fallbackChild, name });
            }
        });
    } else if (commands.length > 1 && process.platform !== "darwin") {
        // 非 macOS 系统，所有其余命令都在当前终端执行
        logWarning("非 macOS 系统，所有命令将在当前终端执行");
        const remainingCommands = commands.slice(1);

        remainingCommands.forEach(({ command, args, name }) => {
            log(`正在当前窗口启动 ${name}...`, "magenta");

            const child = spawn(command, args, {
                stdio: "inherit",
                shell: true,
            });

            child.on("close", (code) => {
                if (code === 0) {
                    logSuccess(`${name} 执行完成`);
                } else {
                    logError(`${name} 执行失败，退出码 ${code}`);
                }
            });

            child.on("error", (error) => {
                logError(`${name} 执行错误: ${error.message}`);
            });

            processes.push({ child, name });
            currentWindowProcesses.push({ child, name }); // 添加到当前窗口进程列表
        });
    }

    // 监听进程退出信号，只清理当前窗口的子进程
    process.on("SIGINT", () => {
        log("\n收到 SIGINT 信号，正在终止当前窗口的子进程...", "yellow");
        currentWindowProcesses.forEach(({ child, name }) => {
            if (!child.killed) {
                log(`正在终止 ${name}...`, "yellow");
                child.kill("SIGINT");
            }
        });
        log("额外窗口中的进程将继续运行，可在各自窗口中独立控制。", "cyan");
        process.exit(0);
    });

    process.on("SIGTERM", () => {
        log("\n收到 SIGTERM 信号，正在终止当前窗口的子进程...", "yellow");
        currentWindowProcesses.forEach(({ child, name }) => {
            if (!child.killed) {
                log(`正在终止 ${name}...`, "yellow");
                child.kill("SIGTERM");
            }
        });
        log("额外窗口中的进程将继续运行，可在各自窗口中独立控制。", "cyan");
        process.exit(0);
    });

    if (commands.length === 1) {
        logSuccess("命令已在当前窗口启动。按 Ctrl+C 停止进程。");
    } else {
        logSuccess(`第一个命令在当前窗口启动，其余 ${commands.length - 1} 个命令已在独立窗口中启动。按 Ctrl+C 停止所有进程。`);
    }
}

// 主要的开发流程
async function runDevFlow(hippyReactVersion, buildCommands, parallelCommands) {
    try {
        log(`\n🚀 正在启动 ${hippyReactVersion} 开发环境`, "bright");

        // 1. 更新依赖
        const dependencyChanged = updateHippyReactDependency(hippyReactVersion);

        // 2. 如果依赖发生变化，执行pnpm install
        if (dependencyChanged) {
            logStep("2", "正在安装依赖");
            await executeCommand("pnpm", ["install"]);
        } else {
            logStep("2", "跳过 pnpm install（依赖未变化）");
        }

        // 3. 执行构建
        logStep("3", "正在构建项目");
        await executeCommand("pnpm", ["run", "build"]);

        // 4. 启动开发服务
        logStep("4", "正在启动开发服务器");
        executeParallelCommands(parallelCommands);
    } catch (error) {
        logError(`开发环境启动失败: ${error.message}`);
        process.exit(1);
    }
}

// dev2 流程
function runDev2() {
    const parallelCommands = [
        {
            command: "pnpm",
            args: ["run", "hippy:dev"],
            name: "Hippy 开发服务器",
        },
        {
            command: "pnpm",
            args: ["run", "hippy:debug"],
            name: "Hippy 调试服务器",
        },
    ];

    runDevFlow(HIPPY_REACT_2X, [], parallelCommands);
}

// dev3 流程
function runDev3() {
    const parallelCommands = [
        {
            command: "pnpm",
            args: ["run", "hippy:dev3"],
            name: "Hippy Dev3 服务器",
        },
    ];

    runDevFlow(HIPPY_REACT_3X, [], parallelCommands);
}

// 显示帮助信息
function showHelp() {
    log("\n📖 开发环境切换器", "bright");
    log("=====================================", "cyan");
    log("");
    log("使用方法:", "yellow");
    log("  npm run dev2    切换到 Hippy React 2.x 并启动调试和开发服务器", "green");
    log("  npm run dev3    切换到 Hippy React 3.x 并启动 dev3 服务器", "green");
    log("");
    log("各命令的功能:", "yellow");
    log("");
    log("dev2:", "cyan");
    log('  1. 更新 @hippy/react 到 "file:third-party/@hippy/react_2.x"');
    log("  2. 运行 pnpm install（如果依赖发生变化）");
    log("  3. 运行 pnpm run build");
    log("  4. 并行启动 pnpm run hippy:debug 和 pnpm run hippy:dev");
    log("");
    log("dev3:", "cyan");
    log('  1. 更新 @hippy/react 到 "file:third-party/@hippy/react"');
    log("  2. 运行 pnpm install（如果依赖发生变化）");
    log("  3. 运行 pnpm run build");
    log("  4. 启动 pnpm run hippy:dev3");
    log("");
}

// 主函数
function main() {
    const command = process.argv[2];

    switch (command) {
        case "dev2":
            runDev2();
            break;
        case "dev3":
            runDev3();
            break;
        case "help":
        case "--help":
        case "-h":
            showHelp();
            break;
        default:
            logError('无效命令。请使用 "dev2"、"dev3" 或 "help"');
            showHelp();
            process.exit(1);
    }
}

// 运行主函数
if (require.main === module) {
    main();
}

module.exports = {
    runDev2,
    runDev3,
    updateHippyReactDependency,
    executeCommand,
};
