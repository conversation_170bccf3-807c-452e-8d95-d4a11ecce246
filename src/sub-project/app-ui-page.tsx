import React, { useState } from "react";
import { Dimensions, Text, View, StyleSheet, ScrollView } from "@hippy/react";
import { BasePage } from "./base-ui";
import { LogUtils } from "./common-base-module/log";
import { Colors, Sizes, TextStyles } from "./theme";
import abcOverlay, { OverlayViewKey } from "./base-ui/views/abc-overlay";
import { BaseComponent } from "./base-ui/base-component";
import { sharedPreferences } from "./base-business/preferences/shared-preferences";
import { AbcIconfont } from "@app/abc-mobile-ui";
import { ABCNavigator } from "./base-ui/views/abc-navigator";
import DemoURLProtocols from "./base-ui/demo-url-dispatcher";

const AppUIOverlayViewLocal = "appUIOverlayViewLocal";

interface AppUIOverlayViewProps {}

interface AppUIOverlayViewState {
    position: { top: number; left: number };
}
const cardData = [
    {
        title: "数据展示",
        children: [
            {
                label: "AbcContentEmpty (空状态)",
                value: DemoURLProtocols.AbcContentEmpty,
            },
            {
                label: "AbcAssetImage (引用本地资源图片)",
                value: DemoURLProtocols.AbcAssetImage,
            },
            {
                label: "AbcBadge (展示徽标数字或小红点)",
                value: DemoURLProtocols.AbcBadge,
            },
        ],
    },
    {
        title: "数据录入",
        children: [
            {
                label: "AbcRadio、AbcRadioButtonGroup (单选按钮、单选组合)",
                value: DemoURLProtocols.AbcRadio,
            },
            {
                label: "AbcStepper (步进器)",
                value: DemoURLProtocols.AbcStepper,
            },
        ],
    },
    {
        title: "导航组件",
        children: [],
    },
    {
        title: "通用组件",
        children: [
            {
                label: "AbcIconfont (图标)",
                value: DemoURLProtocols.AbcIconfont,
            },
        ],
    },
    {
        title: "布局控制",
        children: [
            {
                label: "Flex (flex布局)",
                value: DemoURLProtocols.AbcFlex,
            },
            {
                label: "Grid (grid布局)",
                value: DemoURLProtocols.AbcGrid,
            },
            {
                label: "Divider (分割线)",
                value: DemoURLProtocols.AbcDivider,
            },
        ],
    },
    {
        title: "交互反馈",
        children: [
            {
                label: "Collapse (折叠面板)",
                value: DemoURLProtocols.AbcCollapse,
            },
        ],
    },
    {
        title: "AbcChart",
        children: [],
    },
    {
        title: "其它",
        children: [
            {
                label: "AbcQrCode (二维码)",
                value: DemoURLProtocols.AbcQrCode,
            },
        ],
    },
];

export function AccordionList(): JSX.Element {
    const [openIndex, setOpenIndex] = useState<number | null>(null);

    function handleClickCardItem(item: { label: string; value: string }) {
        ABCNavigator.navigateToPage(item.value).then();
    }

    return (
        <View>
            {cardData.map((card, idx) => (
                <View key={card.title} style={styles.cardContainer}>
                    <View onClick={() => setOpenIndex(openIndex === idx ? null : idx)} style={styles.cardHeader}>
                        <Text style={styles.cardTitle}>{card.title}</Text>
                        <Text style={styles.arrowIcon}>{openIndex === idx ? "▾" : "▸"}</Text>
                    </View>
                    {openIndex === idx && card.children.length > 0 && (
                        <View style={styles.cardContent}>
                            {card.children.map((child) => (
                                <View key={child.value} style={styles.cardItem} onClick={() => handleClickCardItem(child)}>
                                    <Text style={styles.cardItemText}>{child.label}</Text>
                                    <AbcIconfont name={"Arrow_Right"} size={16} color={Colors.P1} />
                                </View>
                            ))}
                        </View>
                    )}
                </View>
            ))}
        </View>
    );
}

const styles = StyleSheet.create({
    cardContainer: {
        marginBottom: 16,
        borderWidth: 1,
        borderColor: "#e2e2e2",
        borderRadius: 12,
        backgroundColor: "#fff",
        overflow: "hidden",
        shadowColor: "#ebedf0",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.08,
        shadowRadius: 12,
        elevation: 2,
        minHeight: 60,
    },
    cardHeader: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        height: 60,
        paddingHorizontal: 24,
        backgroundColor: "#fff",
        shadowColor: "#ebedf0",
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 1,
        shadowRadius: 5,
        elevation: 2,
    },
    cardTitle: {
        fontWeight: "600",
        fontSize: 20,
        color: "#222",
    },
    arrowIcon: {
        fontSize: 30,
        color: "#999",
        marginLeft: 8,
    },
    cardContent: {
        backgroundColor: "#fff",
        paddingHorizontal: 18,
        paddingBottom: 8,
    },
    cardItem: {
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        borderBottomWidth: StyleSheet.hairlineWidth,
        borderBottomColor: "#e2e2e2",
        paddingVertical: 12,
    },
    cardItemText: {
        fontSize: 16,
        color: "#333",
    },
});

export class AppUIOverlayView extends BaseComponent<AppUIOverlayViewProps, AppUIOverlayViewState> {
    touchPointPosition: { top: number; right: number } = { top: 0, right: 0 };

    constructor(props: AppUIOverlayViewProps) {
        super(props);
        const _position = sharedPreferences.getObject(AppUIOverlayViewLocal);
        this.state = {
            position: _position
                ? JSON.parse(_position)
                : {
                      top: Sizes.dp30,
                      left: Dimensions.get("window").width - Sizes.dp70,
                  },
        };
    }

    render(): JSX.Element {
        const { position } = this.state;
        return (
            <View
                style={{
                    position: "absolute",
                    top: position.top,
                    left: position.left,
                    height: Sizes.dp40,
                    width: Sizes.dp40,
                    backgroundColor: "#999",
                    borderRadius: Sizes.dp30,
                }}
                onClick={() => {
                    const { URLProtocols } = require("./url-dispatcher");
                    abcOverlay.hide(OverlayViewKey.uiComponentsPage);
                    ABCNavigator.navigateToPage(URLProtocols.ABC_UI_COMPONENTS).then(() => {
                        AppUIPage.show();
                    });
                }}
                onTouchDown={(e) => {
                    this.touchPointPosition = {
                        top: e.page_y - position.top,
                        right: e.page_x - position.left,
                    };
                }}
                onTouchEnd={(e) => {
                    position.top = e.page_y - this.touchPointPosition.top;
                    position.left = e.page_x - this.touchPointPosition.right;
                    sharedPreferences.setObject(AppUIOverlayViewLocal, JSON.stringify(position));
                    this.setState({ position });
                }}
                onTouchMove={(e) => {
                    position.top = e.page_y - this.touchPointPosition.top;
                    position.left = e.page_x - this.touchPointPosition.right;
                    this.setState({ position });
                }}
                onTouchCancel={() => {
                    sharedPreferences.setObject(AppUIOverlayViewLocal, JSON.stringify(this.state.position));
                }}
            >
                <Text style={[{ textAlign: "center" }, TextStyles.t14NW.copyWith({ lineHeight: Sizes.dp40 })]}>UI</Text>
            </View>
        );
    }
}
interface AppLogPageProps {}
export class AppUIPage extends BasePage<AppLogPageProps> {
    static show(): void {
        try {
            abcOverlay.show(<AppUIOverlayView />, OverlayViewKey.uiComponentsPage);
        } catch (e) {
            LogUtils.e("init = " + JSON.stringify(e));
        }
    }
    static hide(): void {
        abcOverlay.hide(OverlayViewKey.uiComponentsPage);
    }
    getAppBarTitle(): string {
        return "App UI";
    }
    constructor(props: AppLogPageProps) {
        super(props);
    }

    renderContent(): JSX.Element | undefined {
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                <ScrollView style={{ flex: 1, padding: Sizes.dp16 }}>
                    <AccordionList />
                </ScrollView>
            </View>
        );
    }
}
