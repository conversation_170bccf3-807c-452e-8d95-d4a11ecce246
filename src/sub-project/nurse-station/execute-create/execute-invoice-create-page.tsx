/**
 * 成都字节星球科技公司
 *
 * Created by he<PERSON><PERSON> on 2020/9/16
 *
 * @description
 *
 */
import { ABCNetworkPageContentStatus, BaseBlocNetworkPage } from "../../base-ui/base-page";
import { ExecuteInvoiceCreatePageBloc, ScrollToFocusItemState } from "./execute-invoice-create-page-bloc";
import React from "react";
import { ScrollView, Style, Text, View } from "@hippy/react";
import { IconFontView, Spacer } from "../../base-ui";
import { ListSettingItem, ListSettingItemStyle } from "../../base-ui/views/list-setting-item";
import { ABCStyles, Color, Colors, Sizes, TextStyles } from "../../theme";
import { BaseComponent } from "../../base-ui/base-component";
import { ChargeForm, ChargeFormItem, ChargeSourceFormType } from "../../charge/data/charge-beans";
import { EmployeeSearchPage } from "../../charge/employee-search-page";
import WillPopListener from "../../base-ui/views/will-pop-listener";
import _ from "lodash";
import { ChineseMedicineSpecType, GoodsTypeId, HistoryPermissionModuleType, Patient } from "../../base-business/data/beans";
import { PrecisionLimitFormatter } from "../../base-ui/utils/formatter";
import { ActionBtn, StockNotEnoughTextView } from "../../base-ui/views/stock-not-enough-text-view";
import { Const } from "../../base-ui/utils/consts";
import { PharmacyTagView } from "../../outpatient/views/pharmacy-tag-view";
import { CustomInput } from "../../base-ui/input/custom-input";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { AbcPopMenu, MenuItem } from "../../base-ui/views/pop-menu";
import { TreeDotView } from "../../base-ui/iconfont/iconfont-view";
import { AbcButton } from "../../base-ui/views/abc-button";
import { Toast } from "../../base-ui/dialog/toast";
import { userCenter } from "../../user-center";
import { RegistrationFormItem } from "../../registration/data/bean";
import { AbcView } from "../../base-ui/views/abc-view";
import { BlocBuilder } from "../../bloc";
import { OutpatientDiagnosisInputPage } from "../../outpatient/medical-record-page/outpatient-diagnosis-input-page";
import { AbcBasePanel } from "../../base-ui/abc-app-library";
import AbcPatientCardInfoView from "../../outpatient/views/new-patient-Info-view";
import { OutpatientInvoiceStatus } from "../../outpatient/data/outpatient-beans";
import { ExecuteChargeStatusView } from "../../charge/view/charge-views";
import { AbcCardHeader } from "../../base-ui/abc-app-library/common/abc-card-header";
import { ChargeChinesePrescriptionUsageView } from "../../outpatient/views/outpatient-chinese-prescription-usage-view";
import { AbcTextInput } from "../../base-ui/views/abc-text-input";
import abcI18Next from "../../language/config";
import { PsychotropicNarcoticTagView } from "../../views/business-tags";
import { DeviceUtils } from "../../base-ui/utils/device-utils";

enum ExecuteInvoiceMenuItemValue {
    deleteSheet = 1, // 删除执行单
    preview, // 费用预览
}

interface ExecuteInvoiceCreatePageProps {
    patient?: Patient;
    draftId?: string;
    registrationId?: string; //挂号预约id
    chargeSheetId?: string; // 修改执行单id
    registrationDetail?: RegistrationFormItem; //预约详情
}

export class ExecuteInvoiceCreatePage extends BaseBlocNetworkPage<ExecuteInvoiceCreatePageProps, ExecuteInvoiceCreatePageBloc> {
    constructor(props: ExecuteInvoiceCreatePageProps) {
        super(props);

        this.bloc = new ExecuteInvoiceCreatePageBloc({ ...props });
        this.addDisposable(this.bloc);
    }

    getAPPBarCustomTitle(): JSX.Element | undefined {
        return (
            <View>
                <Text style={[TextStyles.t16MT1, { textAlign: "center" }]} numberOfLines={1}>
                    {"治疗理疗开单"}
                </Text>
                {this._renderPatientRegistrationSourceView()}
            </View>
        );
    }

    getAppBarBottomLine(): boolean {
        const { registrationDetail__ } = this.bloc.currentState.detailData ?? {};
        return !registrationDetail__;
    }

    getStatusBarColor(): Color {
        return Colors.panelBg;
    }

    getAppBarBgColor(): Color {
        return Colors.panelBg;
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    onBackClick(): void {
        this.bloc.requestBack();
    }

    componentDidMount(): void {
        this.bloc.state
            .subscribe((state) => {
                let status = ABCNetworkPageContentStatus.show_data;
                if (state.loading) {
                    status = ABCNetworkPageContentStatus.loading;
                }

                this.setContentStatus(status);
            })
            .addToDisposableBag(this);
    }

    renderContent(): JSX.Element | undefined {
        const { detailData, projectAmountStatisticsStr, diagnoseCount, canViewDiagnoseHistory, canSeePatientMobileInExecution } =
            this.bloc.currentState;
        if (!detailData) return <View />;
        const isOhos = DeviceUtils.isOhos();
        return (
            <View style={{ flex: 1, ...(isOhos ? {} : { backgroundColor: Colors.prescriptionBg }) }}>
                <WillPopListener onWillPop={() => this.bloc.requestBack()} />
                <ScrollView style={{ flex: 1 }} showsVerticalScrollIndicator={false}>
                    <AbcPatientCardInfoView
                        isEditing={false}
                        isOutpatient={false}
                        diagnoseCount={diagnoseCount}
                        patientSwitchable={
                            !(!!detailData.isOnline || detailData.source == 1 || detailData?.status == OutpatientInvoiceStatus.visited)
                        }
                        patient={detailData.patient}
                        onChange={(patient) => this.bloc.requestUpdatePatient(patient)}
                        type={HistoryPermissionModuleType.execution}
                        isCanSeePatientHistoryInExecuteStation={canViewDiagnoseHistory}
                        canSeePatientMobileInExecution={canSeePatientMobileInExecution}
                    />

                    {this._renderDiagnosisAndSeller()}
                    {this._renderChargeForm()}

                    <View
                        style={[ABCStyles.rowAlignCenter, { marginHorizontal: Sizes.dp8, height: Sizes.dp58, justifyContent: "flex-end" }]}
                    >
                        <Text style={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}> {projectAmountStatisticsStr}</Text>
                    </View>
                </ScrollView>
            </View>
        );
    }

    getRightAppBarIcons(): JSX.Element[] {
        const { isEditDraft } = this.bloc.currentState;
        return [
            <View key={"all"} style={ABCStyles.rowAlignCenter}>
                <View key={"menu"} style={{ marginRight: Sizes.dp8 }}>
                    {this._renderInvoicePageMenuIcon()}
                </View>
                {(!isEditDraft || isEditDraft) && (
                    <AbcButton
                        style={{ height: Sizes.dp30, width: Sizes.dp52 }}
                        onClick={() => {
                            !isEditDraft ? this.bloc.requestSaveEditDetail() : this.bloc.requestSubmit();
                        }}
                        pressColor={Colors.mainColorPress}
                    >
                        <Text style={[TextStyles.t14MS2, ABCStyles.rowAlignCenter]} numberOfLines={1}>
                            {!isEditDraft ? "保存" : "完成"}
                        </Text>
                    </AbcButton>
                )}
            </View>,
        ];
    }

    private _createMenuItemList(): MenuItem<number>[] {
        const menuItems: MenuItem<number>[] = [];
        const { executeSheetCanDelete } = this.bloc.currentState;

        if (executeSheetCanDelete) {
            menuItems.push(
                new MenuItem({
                    value: ExecuteInvoiceMenuItemValue.deleteSheet,
                    icon: <IconFontView name={"trash"} color={Colors.T1} size={Sizes.dp22} style={{ bottom: -Sizes.dp1 }} />,
                    text: "删除执行单",
                    textStyle: { fontSize: Sizes.dp14 },
                })
            );
        }
        return menuItems;
    }

    private _renderInvoicePageMenuIcon(): JSX.Element {
        const menuItems: MenuItem<number>[] = this._createMenuItemList();
        if (!menuItems.length) return <View />;
        // 外层view对于鸿蒙系统是必要的，否则显示不出来
        return (
            <View>
                <View
                    style={{ paddingHorizontal: Sizes.dp8 }}
                    collapsable={false}
                    onClick={async () => {
                        const select = await AbcPopMenu.show(
                            menuItems,
                            { x: Sizes.dp24, y: Sizes.dp42 },
                            { x: Sizes.dp82, y: Sizes.dp42 },
                            { fullGrid: true }
                        );

                        switch (select) {
                            case ExecuteInvoiceMenuItemValue.preview: {
                                this.bloc.requestPreviewPrice();
                                break;
                            }
                            case ExecuteInvoiceMenuItemValue.deleteSheet: {
                                this.bloc.requestDelete();
                                break;
                            }
                        }
                    }}
                >
                    <TreeDotView color={Colors.T1} />
                </View>
            </View>
        );
    }

    private _renderPatientRegistrationSourceView(): JSX.Element {
        const { detailData } = this.bloc.currentState;
        if (!detailData) return <View />;
        const { registrationDetail__: registrationDetail } = detailData;
        if (!registrationDetail) return <View />;

        const { typeDisplayName } = registrationDetail;

        return (
            <AbcView
                style={ABCStyles.rowAlignCenter}
                onClick={() => {
                    this.bloc.requestShowRegistrationDetail();
                }}
            >
                <Text style={[TextStyles.t11NG1.copyWith({ color: Colors.mainColor, lineHeight: Sizes.dp16 })]}>
                    {`[来源：${typeDisplayName}]`}
                </Text>
            </AbcView>
        );
    }

    private _renderDiagnosisAndSeller(): JSX.Element {
        const { therapySheet, sellerName, diagnosis } = this.bloc.currentState.detailData;
        const { showErrorHint, isDentistry, doctorDisplayStr, nurseDisplayStr, treatmentChargeForm } = this.bloc.currentState;
        const _departmentName = therapySheet?.departmentName || therapySheet?.sellerDepartmentName || "";
        const _doctorDisplayName = therapySheet?.doctorName ?? sellerName ?? "";
        const _configList: any[] = [
            {
                title: "诊断",
                content: therapySheet?.diagnosis ?? diagnosis ?? "",
                contentHint: "输入诊断",
                showErrorBorder: showErrorHint && !therapySheet?.diagnosis,
                onClick: this._onClickDiagnosis.bind(this),
            },
            {
                title: "开单人",
                content: `${_doctorDisplayName}${!!_departmentName ? ` - ${_departmentName}` : ""}`,
                contentHint: "选择开单人",
                showErrorBorder: showErrorHint && !(therapySheet?.doctorName ?? sellerName ?? ""),
                onClick: this._onClickUpdateDoctor.bind(this),
            },
        ];
        if (isDentistry && !!treatmentChargeForm.length) {
            _configList.push({
                title: "医生/护士",
                content: `${doctorDisplayStr}${!!nurseDisplayStr ? "、" : ""}${nurseDisplayStr}` ?? "",
                contentHint: "选择医生/护士",
                onClick: () => {
                    this.bloc.requestModifyProductEmployee();
                },
            });
        }
        return (
            <AbcBasePanel
                panelStyle={{
                    marginTop: Sizes.dp18,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                    backgroundColor: Colors.white,
                    marginHorizontal: Sizes.dp8,
                }}
            >
                {_configList.map((config, index) =>
                    React.createElement(ListSettingItem, {
                        key: index,
                        itemStyle: ListSettingItemStyle.expandIcon,
                        contentStyle: { alignItems: "flex-end" },
                        bottomLine: index != _configList.length - 1,
                        ...config,
                    })
                )}
            </AbcBasePanel>
        );
    }

    private _createFormListView(type: ChargeSourceFormType): JSX.Element[] {
        const { detailData } = this.bloc.currentState;
        const views: JSX.Element[] = [];
        const _formList = detailData.getChargeForm(type)?.chargeFormItems ?? [];
        if (!_formList.length) return [];
        switch (type) {
            case ChargeSourceFormType.treatment:
            case ChargeSourceFormType.examination:
            case ChargeSourceFormType.nurseProductFee:
            case ChargeSourceFormType.package:
            case ChargeSourceFormType.otherFee:
                views.push(
                    ..._formList.map((item, index) => (
                        <_TreatmentFormItem
                            key={item.compareKey()}
                            index={index}
                            length={_formList.length}
                            formItem={item}
                            onItemDelete={(chargeFormItem) => {
                                if (chargeFormItem.drugsHasBeenExecuted) return Toast.show(`已执行不可删除`, { warning: true });
                                this.bloc.requestDeleteChargeFormItem(chargeFormItem);
                            }}
                            onItemChanged={(formItem, count, unit) => {
                                this.bloc.requestChangeMedicineCount(formItem, count, unit);
                            }}
                        />
                    ))
                );
                break;
            case ChargeSourceFormType.goods:
            case ChargeSourceFormType.material:
            case ChargeSourceFormType.glasses:
            case ChargeSourceFormType.westernPrescription:
            case ChargeSourceFormType.infusionPrescription:
                views.push(
                    <_WesternMedicineItemView
                        key={type}
                        chargeForm={detailData.getChargeForm(type)}
                        onItemDelete={(chargeFormItem) => {
                            this.bloc.requestDeleteChargeFormItem(chargeFormItem);
                        }}
                        departmentId={detailData?.departmentId}
                    />
                );
                break;
            case ChargeSourceFormType.chinesePrescription:
                views.push(
                    <_ChineseMedicineItemView
                        key={type}
                        chargeForm={detailData.getChargeForm(type)}
                        onItemDelete={(chargeFormItem) => {
                            this.bloc.requestDeleteChargeFormItem(chargeFormItem);
                        }}
                        departmentId={detailData?.departmentId}
                    />
                );
                break;
        }
        return views;
    }

    private _renderChargeForm(): JSX.Element {
        const { detailData } = this.bloc.currentState;
        const formListKeys: ChargeSourceFormType[] = [
            ChargeSourceFormType.treatment,
            ChargeSourceFormType.examination,
            ChargeSourceFormType.nurseProductFee,
            ChargeSourceFormType.package,
            ChargeSourceFormType.glasses,
            ChargeSourceFormType.goods,
            ChargeSourceFormType.material,
            ChargeSourceFormType.otherFee,
            ChargeSourceFormType.westernPrescription,
            ChargeSourceFormType.infusionPrescription,
            ChargeSourceFormType.chinesePrescription,
        ];
        const hasForm = !!detailData.chargeForms?.length;

        return (
            <AbcBasePanel panelStyle={{ marginHorizontal: Sizes.dp8, marginTop: Sizes.dp18 }}>
                {hasForm && (
                    <>
                        <AbcCardHeader
                            style={{ flex: 1, height: Sizes.dp26, marginTop: Sizes.listHorizontalMargin }}
                            title={"开单项目"}
                            titleStyle={TextStyles.t18MT1.copyWith({ lineHeight: Sizes.dp26 })}
                            showCardLeftLine={false}
                            titleSuffix={() => (
                                <View style={{ marginLeft: Sizes.dp4 }}>
                                    <ExecuteChargeStatusView showChargedStatus={true} chargeStatus={detailData.status} />
                                </View>
                            )}
                        />
                        {formListKeys.map((key) => this._createFormListView(key))}
                    </>
                )}
                <AbcBasePanel
                    panelStyle={{ marginHorizontal: Sizes.dp8 }}
                    contentStyle={[ABCStyles.rowAlignCenter, { justifyContent: "center", padding: Sizes.dp16 }]}
                    onClick={() => this.bloc.requestAddTreatmentItems()}
                >
                    <IconFontView name={"add"} color={Colors.mainColor} size={Sizes.dp14} />
                    <Text style={[TextStyles.t16MM, { marginLeft: Sizes.dp4 }]}>{"添加治疗理疗"}</Text>
                </AbcBasePanel>
            </AbcBasePanel>
        );
    }

    private async _onClickDiagnosis(): Promise<void> {
        const { therapySheet, diagnosis } = this.bloc.currentState.detailData;

        const newDiagnosis = await OutpatientDiagnosisInputPage.show({
            diagnosis: therapySheet?.diagnosis ?? diagnosis,
        });
        if (newDiagnosis != undefined) {
            this.bloc.requestUpdateDiagnosis(newDiagnosis);
        }
    }

    private async _onClickUpdateDoctor(): Promise<void> {
        const { therapySheet } = this.bloc.currentState.detailData;
        const { doctorId, departmentId } = therapySheet ?? {};
        const data = await EmployeeSearchPage.show({
            searchHint: "输入开单人姓名或姓名字母搜索",
            employeeId: doctorId,
            departmentId: departmentId,
        });
        if (!data) return;
        this.bloc.requestUpdateDoctor(data.id, data.name, data.departmentId, data.departmentName);
    }
}

interface _TreatmentFormItemProps {
    formItem: ChargeFormItem;
    chargeFormItems?: ChargeFormItem[];
    onItemDelete?: (item: ChargeFormItem) => void;
    onItemChanged?: (item: ChargeFormItem, count?: number, unit?: string) => void;
    onItemTap?: (item: ChargeFormItem) => void;
    index?: number;
    length?: number;
}

class _TreatmentFormItem extends BaseComponent<_TreatmentFormItemProps> {
    static contextType = ExecuteInvoiceCreatePageBloc.Context;
    private _textAddInput?: AbcTextInput | null;
    private _stockNotEnoughView?: StockNotEnoughTextView | null;

    componentDidMount(): void {
        ExecuteInvoiceCreatePageBloc.fromContext(this.context)
            .state.subscribe((state) => {
                if (
                    state instanceof ScrollToFocusItemState &&
                    state.currentFocusItem &&
                    this.props.formItem.goodsInfo.equals(state.currentFocusItem) &&
                    !this._textAddInput?.focused
                ) {
                    this._textAddInput?.focus();
                }
            })
            .addToDisposableBag(this);
    }

    /**
     * 修改项目备注
     * @param chargeFormItem
     * @param isDelete
     * @private
     */
    private handleModifyChargeItemRemark(chargeFormItem: ChargeFormItem, isDelete: boolean): void {
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        bloc.requestModifyChargeItemRemark(chargeFormItem, isDelete);
    }

    private _createActionBtn(chargeFormItem: ChargeFormItem): ActionBtn[] {
        const { onItemDelete } = this.props;
        const prescriptionRemark = !!chargeFormItem.remark;

        const list: ActionBtn[] = [];
        //切换备注
        if (prescriptionRemark) {
            list.push({
                text: "修改备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, false);
                },
            });
            list.push({
                text: "取消备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, true);
                },
            });
        } else {
            list.push({
                text: "备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, false);
                },
            });
        }

        list.push({
            text: "删除",
            handleClick: () => {
                onItemDelete?.(chargeFormItem);
            },
        });
        return list;
    }

    render() {
        const { formItem, onItemChanged, index, length } = this.props;

        return (
            <View style={{ marginHorizontal: Sizes.listHorizontalMargin }}>
                <AbcView
                    style={{
                        height: formItem.remark ? Sizes.dp68 : Sizes.listItemHeight,
                        marginTop: index == 0 ? Sizes.listHorizontalMargin : undefined,
                        marginBottom: (index ?? 0) + 1 == length ? undefined : Sizes.dp10,
                    }}
                    onLongClick={() => this._stockNotEnoughView?.showOperateTip()}
                >
                    <View style={ABCStyles.rowAlignCenter}>
                        <StockNotEnoughTextView
                            style={{ flexShrink: 1, marginRight: Sizes.dp12 }}
                            textStyle={{ ...TextStyles.t16NT1 }}
                            goodsStock={formItem.stockInfo()}
                            text={`${formItem.displayName}${formItem.isPackage ? "【套餐】" : ""}`}
                            tipContent={this._createActionBtn(formItem)}
                            hasOtherOperate={true}
                            bgColor={"rgba(51, 51, 51, 0.95)"}
                            ref={(ref) => (this._stockNotEnoughView = ref)}
                        />
                        <PsychotropicNarcoticTagView
                            dangerIngredient={formItem.goodsInfo.getIngredientArray}
                            antibiotic={formItem.goodsInfo.getAntibiotic}
                        />
                        <Spacer />
                        <CustomInput
                            ref={(ref) => (this._textAddInput = ref?._textInput)}
                            type={"input"}
                            borderType={"boxBorder"}
                            textStyle={TextStyles.t13NT9.copyWith({ color: Colors.t1 })}
                            style={{ width: Sizes.dp70 }}
                            containerStyle={{ height: Sizes.dp34 }}
                            value={formItem.unitCount ?? 0.0}
                            unit={formItem.displayUnit}
                            unitList={formItem.goodsInfo.sellUnits}
                            onChange={(value) => {
                                onItemChanged?.(formItem, Number(value), undefined);
                            }}
                            onChangeUnit={(unit) => onItemChanged?.(formItem, undefined, unit)}
                            onEndEditing={() => ExecuteInvoiceCreatePageBloc.fromContext(this.context).requestContinueOperate()}
                        />
                    </View>
                    <View style={{ marginTop: -Sizes.dp6, flexShrink: 1, zIndex: 2 }}>
                        <View style={[ABCStyles.rowAlignCenter, { marginRight: Sizes.dp82 }]}>
                            <Text style={TextStyles.t14NT3.copyWith({ color: Colors.t3, lineHeight: Sizes.dp20 })}>
                                {`${abcI18Next.t("¥")}${formItem.goodsInfo.packagePrice}/${formItem.displayUnit}`}
                            </Text>
                        </View>
                        {formItem.remark && (
                            <View style={ABCStyles.rowAlignCenter}>
                                <Text style={TextStyles.t14NT3.copyWith({ color: Colors.t3, lineHeight: Sizes.dp20 })}>
                                    {formItem.remark}
                                </Text>
                            </View>
                        )}
                    </View>
                </AbcView>

                {(index ?? 0) + 1 == length && <View style={[ABCStyles.bottomLine, { height: Sizes.listHorizontalMargin }]} />}
            </View>
        );
    }
}

interface _WesternMedicineItemViewProps {
    chargeForm?: ChargeForm;
    onFocusChanged?: (item: ChargeFormItem, focus: boolean) => void;
    onItemTap?: (item: ChargeFormItem) => void;

    onItemChanged?: (item: ChargeFormItem, count?: number, unit?: string) => void;
    onItemDelete?: (item: ChargeFormItem) => void;
    style?: Style | Style[];
    departmentId?: string;

    showAcuPointsIcon?: boolean; // 是否显示穴位图标
}

class _WesternMedicineItemView extends BaseComponent<_WesternMedicineItemViewProps> {
    static contextType = ExecuteInvoiceCreatePageBloc.Context;
    static defaultProps = { showAcuPointsIcon: false };
    constructor(props: _WesternMedicineItemViewProps) {
        super(props);
    }

    render(): JSX.Element {
        const { chargeForm, style, departmentId, showAcuPointsIcon } = this.props;
        if (!chargeForm?.chargeFormItems?.length) return <View />;
        return (
            <AbcBasePanel contentStyle={{ ...style }}>
                {chargeForm.chargeFormItems?.map((chargeFormItem, index) => (
                    <WesternChargeItemView
                        key={index}
                        chargeForm={chargeForm}
                        chargeFormItem={chargeFormItem}
                        index={index}
                        length={chargeForm.chargeFormItems?.length}
                        onItemChanged={this.props.onItemChanged}
                        onItemDelete={this.props.onItemDelete}
                        onItemTap={this.props.onItemTap}
                        departmentId={departmentId}
                        showAcuPointsIcon={showAcuPointsIcon}
                    />
                ))}
            </AbcBasePanel>
        );
    }
}

interface WesternChargeItemViewProps {
    chargeForm?: ChargeForm;
    chargeFormItem: ChargeFormItem;
    index?: number;
    length?: number;

    onItemTap?: (item: ChargeFormItem) => void;

    onItemChanged?: (item: ChargeFormItem, count?: number, unit?: string) => void;
    onItemDelete?: (item: ChargeFormItem) => void;
    departmentId?: string;

    showAcuPointsIcon?: boolean; // 是否显示穴位图标
}

class WesternChargeItemView extends BaseComponent<WesternChargeItemViewProps> {
    static contextType = ExecuteInvoiceCreatePageBloc.Context;
    private _textAddInput?: AbcTextInput | null;

    private _stockNotEnoughView?: StockNotEnoughTextView | null;

    constructor(props: WesternChargeItemViewProps) {
        super(props);
    }

    static defaultProps = { showAcuPointsIcon: true };

    componentDidMount(): void {
        ExecuteInvoiceCreatePageBloc.fromContext(this.context)
            .state.subscribe((state) => {
                if (
                    state instanceof ScrollToFocusItemState &&
                    state.currentFocusItem &&
                    this.props.chargeFormItem.goodsInfo.scrollKey == state.currentFocusItem!.scrollKey &&
                    !this._textAddInput?.focused
                ) {
                    this._textAddInput?.focus();
                }
            })
            .addToDisposableBag(this);
    }

    render(): JSX.Element {
        const { chargeFormItem, index, length, onItemTap, departmentId, showAcuPointsIcon } = this.props;
        const unitPrice = chargeFormItem.goodsInfo.priceSpecWithUnit(chargeFormItem.displayUnit);
        const goodsInfo = chargeFormItem.goodsInfo;
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        const { pharmacyInfoConfig } = bloc.currentState;
        const defaultPharmacyNo = pharmacyInfoConfig?.getDefaultPharmacy({ departmentId: departmentId, goodsInfo: goodsInfo })?.no;

        return (
            <View style={{ marginHorizontal: Sizes.listHorizontalMargin }}>
                <View
                    style={{
                        marginTop: index == 0 ? Sizes.listHorizontalMargin : undefined,
                        marginBottom: (index ?? 0) + 1 == length ? undefined : Sizes.dp10,
                    }}
                    onClick={() => {
                        onItemTap?.(chargeFormItem);
                    }}
                    key={chargeFormItem.compareKey()}
                    onLongClick={() => this._stockNotEnoughView?.showOperateTip()}
                >
                    <View style={ABCStyles.rowAlignCenter}>
                        <StockNotEnoughTextView
                            style={{ flexShrink: 1, marginRight: Sizes.dp12 }}
                            textStyle={TextStyles.t16NB}
                            goodsStock={chargeFormItem.stockInfo()}
                            text={`${chargeFormItem.name}${goodsInfo.isPackage ? "【套餐】" : ""}`}
                            tipContent={this._createActionBtn(chargeFormItem, showAcuPointsIcon)}
                            hasOtherOperate={true}
                            bgColor={"rgba(51, 51, 51, 0.95)"}
                            ref={(ref) => (this._stockNotEnoughView = ref)}
                        />
                        <PsychotropicNarcoticTagView
                            dangerIngredient={chargeFormItem.goodsInfo.getIngredientArray}
                            antibiotic={chargeFormItem.goodsInfo.getAntibiotic}
                        />
                        <Spacer />
                        <CustomInput
                            ref={(ref) => (this._textAddInput = ref?._textInput)}
                            style={{ width: Sizes.dp70 }}
                            type={"input"}
                            borderType={"boxBorder"}
                            containerStyle={{ height: Sizes.dp34 }}
                            textStyle={TextStyles.t13NT9.copyWith({ color: Colors.t1 })}
                            value={chargeFormItem.unitCount ?? 0.0}
                            unit={chargeFormItem.displayUnit}
                            unitList={chargeFormItem.goodsInfo.sellUnits}
                            onChange={(newValue) => this.handleModifyChargeItemCount(chargeFormItem, Number(newValue))}
                            onChangeUnit={(unit) => this.handleModifyChargeItemUnit(chargeFormItem, unit)}
                            onEndEditing={() => ExecuteInvoiceCreatePageBloc.fromContext(this.context).requestContinueOperate()}
                        />
                    </View>

                    <View style={[ABCStyles.rowAlignCenter, { paddingTop: -Sizes.dp4 }]}>
                        <Text style={[TextStyles.t14NT3.copyWith({ color: Colors.t3 }), { marginRight: Sizes.dp5 }]}>
                            {`${goodsInfo.packageSpec}，${unitPrice}` ?? ""}
                        </Text>
                    </View>
                    <View style={ABCStyles.rowAlignCenter}>
                        {chargeFormItem.remark && (
                            <Text numberOfLines={1} style={[TextStyles.t14NT3.copyWith({ color: Colors.t3 }), { flexShrink: 1 }]}>
                                {chargeFormItem.remark}
                            </Text>
                        )}
                        {pharmacyInfoConfig?.isOpenMultiplePharmacy && (
                            <PharmacyTagView
                                style={{ marginLeft: Sizes.dp4 }}
                                pharmacyNo={chargeFormItem.pharmacyNo}
                                defaultPharmacyNo={defaultPharmacyNo}
                            />
                        )}
                    </View>
                </View>

                {(index ?? 0) + 1 == length && <View style={[ABCStyles.bottomLine, { height: Sizes.listHorizontalMargin }]} />}
            </View>
        );
    }

    /**
     * 删除已添加药品
     * @param chargeFormItem
     * @private
     */
    private handleDeleteChargeItem(chargeFormItem: ChargeFormItem): void {
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        bloc.requestDeleteChargeFormItem(chargeFormItem);
    }

    /**
     * 修改当前药品药房信息
     * @param chargeFormItem
     * @param isDelete
     * @private
     */
    private handleModifyChargeItemPharmacy(chargeFormItem: ChargeFormItem, isDelete: boolean): void {
        const { chargeForm } = this.props;
        if (!chargeForm) return;
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        bloc.requestChangeMedicinePharmacy(chargeForm, chargeFormItem, isDelete);
    }

    /**
     * 修改项目数量
     * @param chargeFormItem
     * @param count
     * @private
     */
    private handleModifyChargeItemCount(chargeFormItem: ChargeFormItem, count: number): void {
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        bloc.requestChangeMedicineCount(chargeFormItem, count);
    }

    /**
     * 修改项目单位
     * @param chargeFormItem
     * @param unit
     * @private
     */
    private handleModifyChargeItemUnit(chargeFormItem: ChargeFormItem, unit: string): void {
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        bloc.requestChangeMedicineCount(chargeFormItem, undefined, unit);
    }

    /**
     * 修改项目备注
     * @param chargeFormItem
     * @param isDelete
     * @param showAcuPointsIcon
     * @private
     */
    private handleModifyChargeItemRemark(chargeFormItem: ChargeFormItem, isDelete: boolean, showAcuPointsIcon?: boolean): void {
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        bloc.requestModifyChargeItemRemark(chargeFormItem, isDelete, showAcuPointsIcon);
    }

    private _createActionBtn(chargeFormItem: ChargeFormItem, showAcuPointsIcon?: boolean): ActionBtn[] {
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        const state = bloc.currentState;
        const prescriptionRemark = !!chargeFormItem.remark;
        const list: ActionBtn[] = [];
        //切换药房处理
        if (state.pharmacyInfoConfig?.isOpenMultiplePharmacy && chargeFormItem.isCanSpecifyPharmacy) {
            list.push({
                text: "药品来源",
                handleClick: () => {
                    this.handleModifyChargeItemPharmacy(chargeFormItem, false);
                },
            });
        }
        //切换备注
        if (prescriptionRemark) {
            list.push({
                text: "修改备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, false, showAcuPointsIcon);
                },
            });
            list.push({
                text: "取消备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, true, showAcuPointsIcon);
                },
            });
        } else {
            list.push({
                text: "备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, false, showAcuPointsIcon);
                },
            });
        }

        list.push({
            text: "删除",
            handleClick: () => {
                this.handleDeleteChargeItem(chargeFormItem);
            },
        });
        return list;
    }
}

class _ChineseMedicineItemView extends BaseComponent<_WesternMedicineItemViewProps> {
    static contextType = ExecuteInvoiceCreatePageBloc.Context;
    private _textAddInput?: AbcTextInput | null;
    private _stockNotEnoughView?: StockNotEnoughTextView | null;

    componentDidMount(): void {
        ExecuteInvoiceCreatePageBloc.fromContext(this.context)
            .state.subscribe((state) => {
                if (
                    state instanceof ScrollToFocusItemState &&
                    state.currentFocusItem &&
                    this.props.chargeForm?.chargeFormItems?.find((t) => t.goodsInfo.equals(state.currentFocusItem!)) &&
                    !this._textAddInput?.focused
                ) {
                    this._textAddInput?.focus();
                }
            })
            .addToDisposableBag(this);
    }

    constructor(props: _WesternMedicineItemViewProps) {
        super(props);
    }

    /**
     * 删除已添加药品
     * @param chargeFormItem
     * @private
     */
    private handleDeleteChargeItem(chargeFormItem: ChargeFormItem): void {
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        bloc.requestDeleteChargeFormItem(chargeFormItem);
    }

    /**
     * 修改项目备注
     * @param chargeFormItem
     * @param isDelete
     * @param showAcuPointsIcon
     * @private
     */
    private handleModifyChargeItemRemark(chargeFormItem: ChargeFormItem, isDelete: boolean, showAcuPointsIcon: boolean): void {
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        bloc.requestModifyChargeItemRemark(chargeFormItem, isDelete, showAcuPointsIcon);
    }
    private _createActionBtn(chargeFormItem: ChargeFormItem, showAcuPointsIcon: boolean): ActionBtn[] {
        const prescriptionRemark = !!chargeFormItem.remark;
        const list: ActionBtn[] = [];

        // 切换备注;
        if (prescriptionRemark) {
            list.push({
                text: "修改备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, false, showAcuPointsIcon);
                },
            });
            list.push({
                text: "取消备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, true, showAcuPointsIcon);
                },
            });
        } else {
            list.push({
                text: "备注",
                handleClick: () => {
                    this.handleModifyChargeItemRemark(chargeFormItem, false, showAcuPointsIcon);
                },
            });
        }

        list.push({
            text: "删除",
            handleClick: () => {
                this.handleDeleteChargeItem(chargeFormItem);
            },
        });
        return list;
    }

    renderChargeItemView(options: { chargeFormItem: ChargeFormItem; index?: number; length?: number }): JSX.Element {
        const { chargeFormItem, index } = options;
        const { chargeForm } = this.props;
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);
        const state = bloc.currentState;
        return (
            <View
                style={{ marginHorizontal: Sizes.listHorizontalMargin, marginTop: index == 0 ? Sizes.listHorizontalMargin : undefined }}
                onLongClick={() => this._stockNotEnoughView?.showOperateTip()}
            >
                <View style={ABCStyles.rowAlignCenter}>
                    <StockNotEnoughTextView
                        style={{ flexShrink: 1, marginRight: Sizes.dp12 }}
                        textStyle={TextStyles.t16NB}
                        text={chargeFormItem.goodsInfo.displayName}
                        goodsStock={chargeFormItem.stockInfo(chargeForm?.pharmacyType)}
                        tipContent={this._createActionBtn(chargeFormItem, false)}
                        bgColor={"rgba(51, 51, 51, 0.95)"}
                        hasOtherOperate={true}
                        ref={(ref) => (this._stockNotEnoughView = ref)}
                    />
                    <Spacer />
                    <CustomInput
                        ref={(ref) => (this._textAddInput = ref?._textInput)}
                        type={"input"}
                        borderType={"boxBorder"}
                        alwaysShowUtil={true}
                        style={{ width: Sizes.dp60 }}
                        containerStyle={{ height: Sizes.dp34 }}
                        textStyle={TextStyles.t13NT9.copyWith({ color: Colors.t1 })}
                        unit={chargeFormItem.unit!}
                        unitList={chargeFormItem.goodsInfo.sellUnits}
                        value={chargeFormItem.unitCount}
                        onChange={(value) =>
                            bloc.requestChangeMedicineCount(chargeFormItem, StringUtils.parseFloat(value)!, chargeFormItem.unit!)
                        }
                        onChangeUnit={(unit) => bloc.requestChangeMedicineCount(chargeFormItem, chargeFormItem.unitCount ?? 0.0, unit)}
                        formatter={PrecisionLimitFormatter(Const.chineseMedicineSellPrecision)}
                        onEndEditing={() => ExecuteInvoiceCreatePageBloc.fromContext(this.context).requestContinueOperate()}
                    />
                </View>
                {state.pharmacyInfoConfig?.isOpenMultiplePharmacy &&
                    chargeForm?.isChinesePrescription &&
                    !!chargeFormItem.goodsInfo?.cMSpec && (
                        <View style={{ marginTop: -Sizes.dp6 }}>
                            <Text style={[TextStyles.t14NT3.copyWith({ color: Colors.t3, lineHeight: Sizes.dp20 })]}>
                                {`${chargeFormItem.goodsInfo?.cMSpec?.slice(-2)}，${chargeFormItem.goodsInfo.priceSpec}`}
                            </Text>
                        </View>
                    )}
                {chargeFormItem.remark && (
                    <View style={ABCStyles.rowAlignCenter}>
                        <Text style={TextStyles.t14NT3.copyWith({ color: Colors.t3, lineHeight: Sizes.dp20 })}>
                            {chargeFormItem.remark}
                        </Text>
                    </View>
                )}
            </View>
        );
    }

    render(): JSX.Element {
        const { chargeForm, style, departmentId } = this.props;
        if (!chargeForm?.chargeFormItems?.length) return <View />;
        const bloc = ExecuteInvoiceCreatePageBloc.fromContext(this.context);

        let pharmacyName; // 未在chargeForm找到pharmacyName时使用
        if (!chargeForm?.pharmacyName) {
            pharmacyName = userCenter.inventoryClinicConfig?.getDefaultPharmacy({
                departmentId: departmentId,
                goodsInfo: {
                    typeId:
                        chargeForm.specification == ChineseMedicineSpecType.fullNames()[0]
                            ? GoodsTypeId.medicineChinesePiece
                            : GoodsTypeId.medicineChineseGranule,
                },
            })?.name;
        }
        const view: JSX.Element[] = [];

        chargeForm.chargeFormItems?.map((chargeFormItem, index) => {
            view.push(
                this.renderChargeItemView({ chargeFormItem: chargeFormItem, index: index, length: chargeForm.chargeFormItems?.length })
            );
        });

        return (
            <View style={{ backgroundColor: Colors.white, ...style }}>
                <View style={{ backgroundColor: Colors.contentBgColor }}>
                    {view}
                    <View style={{ marginHorizontal: Sizes.listHorizontalMargin }}>
                        <ChargeChinesePrescriptionUsageView
                            doseCount={_.first(chargeForm.chargeFormItems)?.doseCount}
                            showErrorHint={false}
                            dosageCountEditable={true}
                            isEditing={true}
                            usageInfo={chargeForm.usageInfo}
                            showRequirement={true}
                            onChangeDosageCount={(count) => bloc.requestChangeDosageCount(chargeForm, count)}
                            onUsageChanged={(usageInfo) => bloc.requestChangeUsage(chargeForm, usageInfo)}
                        />
                        <ListSettingItem
                            itemStyle={ListSettingItemStyle.expandIcon}
                            title={"药房"}
                            titleStyle={TextStyles.t16NT2.copyWith({ color: Colors.t2 })}
                            contentTextStyle={[TextStyles.t16NB, { textAlign: "right" }]}
                            content={chargeForm?.pharmacyName ?? pharmacyName ?? "本地药房"}
                            bottomLine={true}
                            onClick={() => bloc.requestChangeChineseMedicinePharmacy(chargeForm)}
                        />
                    </View>
                </View>
            </View>
        );
    }
}
