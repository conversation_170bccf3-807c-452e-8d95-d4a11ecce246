import { callNativeWithPromise } from "@hippy/react";

export type FileEncodingType = "utf8" | undefined | "base64";

/**
 * 对接native端文件操作api
 */
export default class FileManager {
    /**
     * 判断文件是否存在
     * @param file
     * @return Promise<boolean> true存在，false不存在
     */
    static exists(file: string): Promise<boolean> {
        if (!file) return Promise.resolve(false);

        return callNativeWithPromise("FileManager", "exists", file);
    }

    /**
     * 将字符按指定编码写入文件
     * @param file 要写入的文件
     * @param content 写入的内容
     * @param encode 编码方式
     * @return Promise<boolean> true写入成功，注：可能出现异常
     */
    static writeAsString(file: string, content: string, encode?: FileEncodingType): Promise<boolean> {
        if (!file) return Promise.reject(`文件${file}不存在`);
        encode = encode != undefined ? encode : "utf8";
        return callNativeWithPromise("FileManager", "writeAsString", file, content || "", encode);
    }

    /**
     * 以字符串方式读取文件
     * @param file
     * @param encode
     * @return Promise<string> 返回读取的内容，如果失败将会抛出异常
     */
    static readAsString(file: string, encode: FileEncodingType): Promise<string> {
        if (!file) return Promise.reject(`文件${file}不存在`);
        encode = encode != undefined ? encode : "utf8";
        return callNativeWithPromise("FileManager", "readAsString", file, encode != undefined ? encode : "utf8");
    }

    /**
     *创建文件夹
     * @param path 文件夹路径
     * @return Promise<string> true表示成功，其它将会以reject异常方式返回
     */
    static mkdirs(path: string): Promise<boolean> {
        if (!path) return Promise.reject(`目录${path}不存在`);
        return callNativeWithPromise("FileManager", "mkdirs", path);
    }

    /**
     *删除文件
     * @param path 要删除的文件
     * @return Promise<string> true表示成功，其它将会以reject异常方式返回
     */
    static delete(path: string): Promise<boolean> {
        if (!path) return Promise.reject(`目录${path}不存在`);

        return callNativeWithPromise("FileManager", "delete", path);
    }

    /**
     * 文件移动
     * @param from 源文件
     * @param to 目标文件
     * @param Promise<boolean> true:移动成功，失效将以reject异常方式反回
     */
    static mv(from: string, to: string): Promise<boolean> {
        if (!from) return Promise.reject(`目录${from}不存在`);
        if (!to) return Promise.reject(`目录${to}不存在`);

        return callNativeWithPromise("FileManager", "mv", from, to);
    }

    /**
     * 获取应用的文档存储路径
     * @return Promise<string>文档存储根路径
     */
    static getApplicationDocumentsDirectory(): Promise<string> {
        return callNativeWithPromise("FileManager", "getApplicationDocumentsDirectory");
    }

    /**
     * 获取临时文件存储根路径
     * @return Promise<string>文档存储根路径
     */
    static getTemporaryDirectory(): Promise<string> {
        return callNativeWithPromise("FileManager", "getTemporaryDirectory");
    }
}
