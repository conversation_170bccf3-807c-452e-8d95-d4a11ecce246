/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-19
 *
 * @description
 */
import URL, { UrlWithParsedQuery } from "url";

export default class UrlUtils {
    static getUrlParams(url: string): Map<string, string> {
        const { query } = URL.parse(url, true);
        const map = new Map<string, string>();
        Object.keys(query).forEach((key) => {
            map.set(key, query[key] as string);
        });
        return map;
    }

    static getHost(url: string): string | null {
        return URL.parse(url).host;
    }

    /**
     * 去掉oss 连接中图参片的压缩数，以获取到原图地址
     * @param url
     */
    static tripOSSCompressParams(url: string): string {
        const { protocol, host, pathname, query } = URL.parse(url, true);
        // 有签名的地址，直接返回原始地址
        if (query["abc_signature"]) {
            return url;
        }
        delete query["x-oss-process"];
        return URL.format({
            protocol,
            slashes: true,
            host,
            pathname,
            query,
        });
    }

    static getUrlPath(url: string): string {
        const { protocol, host, pathname } = URL.parse(url);
        return URL.format({
            protocol,
            slashes: true,
            host,
            pathname,
        });
    }

    static httpString(s: string): string {
        const reg = /(https?|http|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/g;
        return s.match(reg)?.[0] ?? "";
    }

    //使用正则解析一个url地址，返回一个对象，包括host、port、path、query、hash, query和hash是对象
    static parseUrl(url: string): UrlWithParsedQuery | null {
        return URL.parse(url, true, true);
    }
}
