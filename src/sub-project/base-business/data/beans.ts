/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */
import { LogUtils } from "../../common-base-module/log";
import _, { isNil } from "lodash";
import { defaultIfNil, NumberUtils } from "../../common-base-module/utils";
import {
    dateToYyyyMMddString,
    fromBrToN,
    fromJsonToDate,
    JsonMapper,
    JsonProperty,
} from "../../common-base-module/json-mapper/json-mapper";
import { ABCUtils } from "../../base-ui/utils/utils";
import { DiseasesCode } from "../mix-agent/data/shebao-bean";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { ToothNos } from "../../data/tooth-bean";
import { MedicalRecordUtils } from "../../outpatient/medical-record-page/utils/medical-record-utils";
import infectiousDiseases from "../../../assets/medicine_usage/infectious-diseases";
import abcI18Next from "../../language/config";
import { MedicineCategoryItem } from "../../inventory/inventory-goods/data/inventory-goods-agent";
import { BusinessScopeList } from "./clinic-data";
export const BATCH_LOCKING_METHOD = "按锁批次方式";
export enum GoodsTypeId {
    medicine = 1, // 药品
    material = 2, // 物资
    examination = 3, // 检查检验
    treatmentAndPhysiotherapy = 4, // 治疗理疗
    registration = 5, // 挂号
    memberCard = 6, // 会员卡
    goods = 7, // 商品
    package = 8, // 套餐
    consultFee = 9, // 在线问诊
    deliveryFee = 10, // 快递费
    decoctionFee = 11, // 煎药费
    medicineWest = 12, // 西药
    medicineChinese = 13, // 中药
    medicineChinesePiece = 14, // 中药饮片
    medicineChineseGranule = 15, // 中药颗粒
    medicineChinesePatent = 16, //中成药
    materialMedical = 17, // 医疗器械
    materialDisinfectant = 91, // 消毒用品
    materialLogistics = 18, // 后勤材料
    materialFixedAssets = 19, // 固定资产
    examine = 20, // 检验
    test = 21, // 检查
    physiotherapy = 22, // 治疗
    treatment = 23, // 理疗
    other = 24, // 其他
    goodsHomemade = 25, // 自制成品
    healthMedicine = 26, // 保健药品
    healthGood = 27, // 保健食品
    cosmetics = 92, // 化妆品
    otherGood = 28, // 其他商品
    otherGoods49 = 33, // 其他费用
    nurseProduct = 56, // 护理项目
    eyeGlass = 64, //眼镜片
    mirrorFrame = 65, //镜架
    orthokeratoscope = 66, //角膜塑形镜
    softHydrophilicMirror = 67, //软性亲水镜
    rigidOxygenPermeator = 68, //硬性透氧镜
    sunglasses = 69, //太阳镜
    medicineChineseNonFormula = 93, //非配方饮片
}

export const goodsTypeIds = [
    {
        id: 0,
        type: "",
        subType: "",
        cMSpec: "",
        label: "全部类型",
    },
    {
        id: GoodsTypeId.medicine,
        type: 1,
        subType: "",
        cMSpec: "",
        label: "全部药品",
    },
    {
        id: GoodsTypeId.medicineWest,
        type: 1,
        subType: 1,
        cMSpec: "",
        label: "西药",
    },
    {
        id: GoodsTypeId.medicineChinesePatent,
        type: 1,
        subType: 3,
        cMSpec: "",
        label: "中成药",
    },
    {
        id: GoodsTypeId.medicineChinesePiece,
        type: 1,
        subType: 2,
        cMSpec: "中药饮片",
        label: "中药饮片",
    },
    {
        id: GoodsTypeId.medicineChineseGranule,
        type: 1,
        subType: 2,
        cMSpec: "中药颗粒",
        label: "中药颗粒",
    },
    {
        id: GoodsTypeId.material,
        type: 2,
        subType: "",
        cMSpec: "",
        label: "全部物资",
    },
    {
        id: GoodsTypeId.materialMedical,
        type: 2,
        subType: 1,
        cMSpec: "",
        label: "医疗器械",
    },
    {
        id: GoodsTypeId.materialLogistics,
        type: 2,
        subType: 2,
        cMSpec: "",
        label: "后勤材料",
    },
    {
        id: GoodsTypeId.materialFixedAssets,
        type: 2,
        subType: 3,
        cMSpec: "",
        label: "固定资产",
    },
    {
        id: GoodsTypeId.materialDisinfectant,
        type: 2,
        subType: 4,
        cMSpec: "",
        label: "消毒用品",
    },
    {
        id: GoodsTypeId.goods,
        type: 7,
        subType: "",
        cMSpec: "",
        label: "全部商品",
    },
    {
        id: GoodsTypeId.goodsHomemade,
        type: 7,
        subType: 1,
        cMSpec: "",
        label: "自制成品",
    },
    {
        id: GoodsTypeId.healthMedicine,
        type: 7,
        subType: 2,
        cMSpec: "",
        label: "保健药品",
    },
    {
        id: GoodsTypeId.healthGood,
        type: 7,
        subType: 3,
        cMSpec: "",
        label: "保健食品",
    },
    {
        id: GoodsTypeId.otherGood,
        type: 7,
        subType: 4,
        cMSpec: "",
        label: "其他商品",
    },
    {
        id: GoodsTypeId.cosmetics,
        type: 7,
        subType: 5,
        cMSpec: "",
        label: "化妆品",
    },
    {
        id: GoodsTypeId.otherGoods49,
        type: 19,
        subType: 0,
        cMSpec: "",
        label: "其他费用",
    },
    {
        id: GoodsTypeId.eyeGlass,
        type: 24,
        subType: 1,
        cMSpec: "",
        label: "镜片",
    },
    {
        id: GoodsTypeId.mirrorFrame,
        type: 24,
        subType: 2,
        cMSpec: "",
        label: "镜架",
    },
    {
        id: GoodsTypeId.orthokeratoscope,
        type: 24,
        subType: 3,
        cMSpec: "",
        label: "角膜塑形镜",
    },
    {
        id: GoodsTypeId.softHydrophilicMirror,
        type: 24,
        subType: 4,
        cMSpec: "",
        label: "软性亲水镜",
    },
    {
        id: GoodsTypeId.rigidOxygenPermeator,
        type: 24,
        subType: 5,
        cMSpec: "",
        label: "硬性透氧镜",
    },
    {
        id: GoodsTypeId.sunglasses,
        type: 24,
        subType: 6,
        cMSpec: "",
        label: "太阳镜",
    },
];

/**
 * 商品类别
 * types: number[];
 * subTypes: number[];
 * spec: string[];
 */
export interface GoodsTypes {
    types: number[];
    subTypes: number[];
    spec: string[];
}

export class GoodsTypeObj {
    type?: number;
    subType?: number;
    cMSpec?: string;
}

export class GoodsType {
    static medicine = 1; // 药品
    static material = 2; // 物资
    static examination = 3; //检查检验
    static treatment = 4; //治疗，理疗
    static registration = 5; //
    static memberCard = 6; //会员卡
    static goods = 7; //商品 ,这个名字跟GoodsType取得有点冲突，就这么着吧
    static package = 11; //套餐

    static consultFee = 12; //咨询费

    static deliveryFee = 13; //快递费

    static decoctionFee = 14; //代煎

    static ingredient = 15; //辅料费

    static familyDoctorFee = 16; //家庭医生签约费

    static otherGoods49 = 19; //其他费用(49号项目)

    static nurseProduct = 21; //护理项目

    static glasses = 24; //眼镜

    static surgery = 29; //手术

    static typeIds2GoodsTypeAndSubType(typeIds: number[]): GoodsTypes {
        const types: number[] = [],
            subTypes: number[] = [],
            spec: string[] = [];
        typeIds.forEach((item) => {
            switch (item) {
                case GoodsTypeId.medicine: {
                    types.push(1);
                    // subTypes.push(0);
                    break;
                }
                case GoodsTypeId.material: {
                    types.push(2);
                    // subTypes.push(0);
                    break;
                }

                case GoodsTypeId.examination: {
                    types.push(3);
                    // subTypes.push(0);
                    break;
                }
                case GoodsTypeId.treatmentAndPhysiotherapy: {
                    types.push(4);
                    // subTypes.push(0);
                    break;
                }
                case GoodsTypeId.registration: {
                    types.push(5);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.memberCard: {
                    types.push(6);
                    // subTypes.push(0);
                    break;
                }
                case GoodsTypeId.package: {
                    types.push(11);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.consultFee: {
                    types.push(12);
                    // subTypes.push(0);
                    break;
                }
                case GoodsTypeId.deliveryFee: {
                    types.push(13);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.decoctionFee: {
                    types.push(14);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.medicineWest: {
                    types.push(1);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.medicineChinese: {
                    types.push(1);
                    subTypes.push(2);
                    break;
                }
                case GoodsTypeId.medicineChinesePiece: {
                    types.push(1);
                    subTypes.push(2);
                    spec.push("中药饮片");
                    break;
                }
                case GoodsTypeId.medicineChineseGranule: {
                    types.push(1);
                    subTypes.push(2);
                    spec.push("中药颗粒");
                    break;
                }
                case GoodsTypeId.medicineChinesePatent: {
                    types.push(1);
                    subTypes.push(3);
                    break;
                }
                case GoodsTypeId.materialMedical: {
                    types.push(2);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.materialLogistics: {
                    types.push(2);
                    subTypes.push(2);
                    break;
                }
                case GoodsTypeId.materialFixedAssets: {
                    types.push(2);
                    subTypes.push(3);
                    break;
                }
                case GoodsTypeId.materialDisinfectant: {
                    types.push(2);
                    subTypes.push(4);
                    break;
                }
                case GoodsTypeId.examine: {
                    types.push(3);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.test: {
                    types.push(3);
                    subTypes.push(2);
                    break;
                }
                case GoodsTypeId.physiotherapy: {
                    types.push(4);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.treatment: {
                    types.push(4);
                    subTypes.push(2);
                    break;
                }
                case GoodsTypeId.other: {
                    types.push(4);
                    subTypes.push(9);
                    break;
                }
                case GoodsTypeId.goodsHomemade: {
                    types.push(7);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.healthMedicine: {
                    types.push(7);
                    subTypes.push(2);
                    break;
                }
                case GoodsTypeId.healthGood: {
                    types.push(7);
                    subTypes.push(3);
                    break;
                }
                case GoodsTypeId.otherGood: {
                    types.push(7);
                    subTypes.push(4);
                    break;
                }
                case GoodsTypeId.cosmetics: {
                    types.push(7);
                    subTypes.push(5);
                    break;
                }
                case GoodsTypeId.otherGoods49: {
                    types.push(19);
                    subTypes.push(0);
                    break;
                }
                case GoodsTypeId.eyeGlass: {
                    types.push(24);
                    subTypes.push(1);
                    break;
                }
                case GoodsTypeId.mirrorFrame: {
                    types.push(24);
                    subTypes.push(2);
                    break;
                }
                case GoodsTypeId.orthokeratoscope: {
                    types.push(24);
                    subTypes.push(3);
                    break;
                }
                case GoodsTypeId.softHydrophilicMirror: {
                    types.push(24);
                    subTypes.push(4);
                    break;
                }
                case GoodsTypeId.rigidOxygenPermeator: {
                    types.push(24);
                    subTypes.push(5);
                    break;
                }
                case GoodsTypeId.sunglasses: {
                    types.push(24);
                    subTypes.push(6);
                    break;
                }
            }
        });
        return {
            types: types,
            subTypes: subTypes,
            spec: spec,
        };
    }

    static GoodsTypeAndSubType2TypeId(params: { type?: number; subType?: number; cMSpec?: string }): GoodsTypeId | undefined {
        const current = goodsTypeIds.find(
            (item) => item.type == (params.type ?? "") && item.subType == (params.subType ?? "") && item.cMSpec == (params.cMSpec ?? "")
        );
        return current?.id;
    }
}

export class GoodsSubType {
    static medicineWestern = 1; //西药
    static medicineChinese = 2; //中药
    static medicineChinesePatent = 3; //中成药

    static materialMedical = 1; //医疗器械
    static materialDisinfectant = 4; //消毒用品
    static materialLogistics = 2; //后勤材料
    static materialFixedAssets = 3; //固定资产

    static examination = 1; //检查检验-检验
    static examinationTest = 2; //检查检验-检查

    static treatment = 1; //治疗
    static treatmentPhysiotherapy = 2; //理疗
    static treatmentOther = 9; //理疗-其它

    static registrationDefault = 1; //挂号默认值

    //商品7类型下的子类型
    static goodsHomemade = 1; //自制商品
    static healthMedicine = 2; // 保健药品
    static healthGood = 3; // 保健食品
    static cosmetics = 5; // 化妆品
    static otherGood = 4; // 其他商品

    static packageTreatment = 1; //套餐-理疗

    static deliveryFeeDefault = 1; //快递费
    static decoctionFeeDefault = 1; //快递费

    //其他费用(49号项目)类型下的子类型
    static otherGoods49Sub = 0;

    static eyeGlass = 1; //眼镜片
    static eyeGlassFrame = 2; //眼镜架
    static orthokeratoscope = 3; //角膜塑形镜
    static softHydrophilicMirror = 4; //软性亲水镜
    static rigidOxygenPermeator = 5; //硬性透氧镜
    static sunglasses = 6; //太阳镜
    static surgery = 1; //手术
}

export enum ChineseGoodType {
    chinesePiece = 14, //饮片
    chineseGranule = 15, //颗粒
}
export interface ShebaoNationalView {
    medicalFeeGrade?: number;
    medicalFeeGradeName?: string;
    shebaoCode?: string;
    shebaoCodeWarnFlag?: string;
    shebaoExpiredWarnFlag?: string;
    shebaoOverPriceStatus?: number;
    shebaoOverPriceWarnFlag?: number;
    shebaoUsage?: number;
}
export enum SubClinicPricePriceMode {
    sellingPrice = 1, // 固定售价
    purchaseMarkup = 3, // 进价加成
}
export class SubPriceFlag {
    /**
     * 连锁统一定价
     * */
    static UNIFY_FIXED_PRICE = 0;
    /**
     * 门店自主定价：总部定
     * */
    static SEPARATE_FIXED_PRICE_AND_ADMIN_CLINIC = 21;
    /**
     * 门店自主定价：门店定
     * */
    static SEPARATE_FIXED_PRICE_AND_SUB_CLINIC = 2;

    /**
     * 诊所下：门店无定价权-总部定，这个值无法由用户设置，是后端直接下发的
     * */
    static UNIFY_FIXED_PRICE_AND_ADMIN_CLINIC = 3;
}

export class StockInfo {
    stockEnough: boolean;
    tips?: string;

    constructor(stockEnough: boolean, tips?: string) {
        this.stockEnough = stockEnough;
        this.tips = tips;
    }
}

export class ChineseMedicineSpecType {
    static chinesePiece = 0; //中药饮片
    static chineseGranule = 1; //中药颗粒

    static displayNames(): Array<string> {
        return ["饮片", "颗粒"];
    }

    static fullNames(): Array<string> {
        return ["中药饮片", "中药颗粒"];
    }

    static typeFromName(name = ""): number {
        const index: number = this.displayNames().indexOf(name);
        if (index >= 0) return index;

        if (name == "中药颗粒") return this.chineseGranule;

        return this.chinesePiece;
    }
}

export class SupplierDetail {
    name?: string;
}

function convertToComposeChildren(json: any) {
    let children: GoodsInfo[] = [];
    if (json) children = json.map((item: any) => JsonMapper.deserialize(GoodsInfo, item));

    return children;
}

export enum GoodsInfoV2DisableStatus {
    normal = 0, //正常状态
    afterSale = 10, //库存售完停用（库存变成0后，disable=1）
    immediately = 20, //立即停用 （库存变成0后，disable=1）
}

export class GoodsInfoShebao {
    goodsId?: string | null;
    goodsType?: GoodsTypeId | null;
    medicineNum?: string | null;
    name?: string | null;
    shebaoCode?: string | null;
    standardCode?: string | null;
    materialUniversalCode?: string | null;
    priceLimit?: string | null;
    medicalFeeGrade?: number | null;
    ownExpenseRatio?: string | null;
    nationalStandardCode?: string | null;

    isDummy?: number;
    nationalCode?: string; // 国家编码
    nationalCodeId?: string; // 国家编码ID
}

export class PharmacyGoodsStockInfo {
    pharmacyNo?: number;
    pharmacyName?: string;
    stockPackageCount?: number;
    stockPieceCount?: number;
    lastPackageCostPrice?: number;
}

export class GoodsStockInfo {
    lockingPackageCount?: number;
    lockingPieceCount?: number;
    outPackageCount?: number;
    outPieceCount?: number;
    packageCount?: number;
    packageUnit?: string;
    pieceCount?: number;
    pieceNum?: number;
    pieceUnit?: string;
    stockPackageCount?: number;
    stockPieceCount?: number;
    stockStatusName?: string;

    availablePackageCount?: number;
    availablePieceCount?: number;
    dispGoodsCount?: string;
    dispLockingGoodsCount?: string;
    dispOutGoodsCount?: string;
    dispProhibitGoodsCount?: string;
    dispStockGoodsCount?: string;

    goodsId?: string;
    goodsDispName?: string;
    displaySpec?: string;
}
export class ExaminationResult {
    adviceExecuteItemId?: string;
    chargeFormItemId?: string;
    chargeSheetId?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    examinationApplySheetId?: string;
    examinationName?: string;
    examinationSheetId?: string;
    mergeSheetId?: string;
    outpatientFormItemId?: string;
    patientOrderId?: string;
    sampleStatus?: number;
    status?: number;
    type?: number;
}
export class GoodsBatchInfoList {
    batchId?: number; // 批次
    batchNo?: string; // 批号
    cutPackageCount?: number; // 能够扣除的库存量（大单位）这两者不会同时存在，会跟随当前发药的数量单位来决定
    cutPieceCount?: number; // 能够扣除的库存量（小单位）
    dispGoodsCount?: string;
    dispLockingGoodsCount?: string;
    dispStockGoodsCount?: string;
    @JsonProperty({ toJson: dateToYyyyMMddString })
    expiryDate?: Date; // 效期
    @JsonProperty({ toJson: fromJsonToDate })
    inDate?: Date;
    lockingPackageCount?: number;
    lockingPieceCount?: number;
    packageCostPrice?: number;
    packageCount?: number;
    packagePrice?: number;
    pharmacyNo?: number;
    pharmacyType?: number;
    pieceCount?: number;
    pieceNum?: number;
    piecePrice?: number;
    @JsonProperty({ toJson: dateToYyyyMMddString })
    productionDate?: Date;
    status?: number;
    stockPackageCount?: number;
    stockPieceCount?: number;
    supplierId?: string;
    supplierName?: string;
    totalSalePrice?: number; // 售价
    cutTotalPieceCount?: number;
    lockInfo?: {
        lockId?: string;
        lockLeftTotalPieceCount?: number;
        lockingPackageCount?: number;
        lockingPieceCount?: number;
        pharmacyNo?: number;
        pharmacyType?: number;
    };
}
export class FormItemBatchInfos {
    batchId?: string;
    batchNo?: string;
    expiryDate?: Date;
    id?: string;
    isUseLimitPrice?: number;
    productId?: string;
    receivablePrice?: number;
    refundTotalPrice?: number;
    refundUnitCount?: number;
    sourceTotalPrice?: number;
    stockId?: string;
    totalPrice?: number;
    unitCostPrice?: number;
    unitCount?: number;
    unitPrice?: number;
}
export class DispensingFormItemBatch {
    id?: string; //发药批次ID
    batchId?: number; //批次ID
    batchNo?: string; //批次号
    totalPrice?: number; //总价格
    sourceTotalPrice?: number; //商品原价
    unitPrice?: number; //售价
    unitCount?: number; //单位数量
    doseCount?: number; //剂数
    @JsonProperty({ type: GoodsBatchInfoList })
    batchInfo?: GoodsBatchInfoList;
    __checked?: boolean; //终端自定义属性，用于标识当前批次是否选中
    __unitCount?: number; //终端自定义，用于存储当前次数
}
export class GoodsLockBatchItemList {
    batchId?: number;
    batchNo?: string;
    expiryDate?: Date;
    goodsId?: string;
    goodsName?: string;
    lockFormItemId?: string;
    lockingBatchPackageCount?: number;
    lockingBatchPieceCount?: number;
    refId?: string;
    unitCostPrice?: number;
}
class PurchasePriceRange {
    maxPackagePrice?: number;
    minPackagePrice?: number;
    maxPiecePrice?: number;
    minPiecePrice?: number;
}
export class TraceableCodeNoInfo {
    drugIdentificationCode?: string; //药品唯一标识码
    traceableCodeType?: number;
    no?: string; //追溯码
    serialNumber?: string;
    type?: number; // 追溯码码类型 0/null： 普通嘛，10：无码
}

/**
 * 库存标签
 */
export class GoodsTagListItem {
    tagId?: string; // 标签ID
    name?: string; // 标签名称
    clinicId?: string; // 诊所ID (有ID为门店创建/没有ID为总店创建)
}
export enum MemberPriceDiscountType {
    Discount = 0, // 折扣
    SpecialPrice = 1, // 特价
    NoDiscount = 10, // 无折扣(默认)
}
export enum GoodsMultiPriceOpType {
    Add = 0, // add
    Update = 1, // update
    Delete = 2, // delete
}
export enum GoodsMultiPriceType {
    Normal = 0, // 普通的价格体系
    PhysicalExamPharmacy = 1, // 体检合作药房价格
    Discount = 10, // 折扣价体系
}
export class GoodsMultiPriceView {
    clinicId?: string; // 门店ID 如果是门店的自定义折扣，传门店ID/单店连锁位总部ID
    discountType?: MemberPriceDiscountType; // 折扣价体系 折扣类型 0 折扣， 1 特价 10 无折扣：默认，会员等级默认使用设置在类型上的统一优惠
    discountValue?: number; // 折扣价体系 折扣值: 折扣0.9(9折)
    id?: number;
    individualPricingType?: number;
    memberCardName?: string; // 折扣价体系 会员卡名字
    memberTypeId?: string; // 折扣价体系 会员id
    opType?: GoodsMultiPriceOpType; // 操作类型 0 add 1 update 2 delete
    ownType?: number; // 输出-价格谁所有 0 门店价格 1 总部/连锁价格
    packageCostPrice?: number;
    packagePrice?: number; // 折扣价体系 折扣价体系 特价:9元
    piecePrice?: number; // 折扣价体系 折扣价体系 特价:9元
    priceMakeupPercent?: number;
    priceType?: number; // 价格类型 1 价格 3进价加成
    subPriceFlag?: number;
    targetType?: GoodsMultiPriceType; // 多价格类型 0 普通的价格体系；1 体检合作药房价格； 10 折扣价体系
}
export class GoodsMultiPriceViewReq {
    CMSpec?: string; // 空字符串pc端也需要，不要null
    goodsId?: string; // 药品ID 可选
    goodsType?: number;
    goodsSubType?: number;
    customTypeId?: number; // goods上的二级分类
}

export class GoodsInfo {
    id?: string;
    shortId?: string;
    organId?: string;
    name?: string;
    displaySpec?: string;
    py?: string;
    medicineCadn?: string;
    type?: number; // GoodsType
    typeId?: number;
    subType?: number; // GoodsSubType
    customTypeName?: string; //二级分类
    dismounting?: number;
    manufacturer?: string;
    manufacturerFull?: string;
    isSell?: number;

    packagePrice?: number;

    packageCostPrice?: number;

    piecePrice?: number;

    /**
     * specType 药品存在多规格类型时指定规格种类
     * null 无多规格
     * 0    西药 剂量模式
     * 1    西药 容量模式
     */
    specType?: number; // 规格类型 add by jianglei 2022.04.26

    pieceNum?: number;
    pieceUnit?: string;
    packageUnit?: string;
    medicineDosageNum?: number;
    medicineDosageUnit?: string;
    medicineDosageForm?: string;
    componentContentUnit?: string; // 成分单位 add by jianglei 2022.04.26
    componentContentNum?: number; // 成分数量 add by jianglei 2022.04.26
    medicineNmpn?: string; //国药准字
    materialSpec?: string;
    cMSpec?: string; //门诊单保存当前药品（中药）颗粒/饮片 状态
    __cMSpec?: string; //终端使用字段，保存药品转换前的类型
    grade?: string;
    origin?: string; //物资才有(进口/国产)

    @JsonProperty({ type: SupplierDetail })
    supplier?: SupplierDetail; // 供应商
    certificateName?: string; //注册证名称
    certificateNo?: string; //注册证号

    //  position;
    remark?: string;

    // 终端自定义字段---检查项目是否存在已完成的
    __hasResultExaminations?: boolean;
    @JsonProperty({ type: Array, clazz: ExaminationResult })
    __examinationResult?: ExaminationResult[];
    __executeStatus?: number;
    __executeStatusName?: string;
    __outpatientSheetId?: string;
    __productFormId?: string;
    __id?: string;

    //  smartDispense?:string;
    barCode?: string;
    status?: number;
    inTaxRat?: number;
    outTaxRat?: number;

    chainPiecePrice?: number;
    chainPackagePrice?: number;
    chainPackageCostPrice?: number;
    stockPieceCount?: number;
    stockPackageCount?: number;
    lastPackageCostPrice?: number;
    pieceCount?: number;

    packageCount?: number;

    noStocks?: boolean;
    bizExtension?: any;

    //中药规格
    extendSpec?: string;
    position?: string;
    defaultInOutTax?: number; //进/销项税率是否更改（1--不可修改，0---可以修改）

    @JsonProperty({ fromJson: convertToComposeChildren })
    children?: GoodsInfo[];

    operationCode?: string;
    isPreciousDevice?: number;
    surgerySiteCode?: string;
    surgeryIncisionHealingCode?: string;
    surgeryGradeCode?: string;
    expiredWarnMonths?: number;
    cloudSupplierFlag?: number;
    usePieceUnitFlag?: number;
    copiedFlag?: number;
    coopFlag?: number;
    deviceInnerFlag?: number;
    innerFlag?: number;
    chainDisable?: number;
    surgeryDetail?: SurgeryRequest; // 手术相关的申请单
    @JsonProperty({ fromJson: convertToComposeChildren })
    composeChildren?: GoodsInfo[];

    //套餐使用字段
    composePackageCount?: number;
    composePackagePrice?: number;
    composePieceCount?: number;
    composePiecePrice?: number;
    composePrice?: number;
    composeUseDismounting?: number;

    //知否可执行
    needExecutive?: number;

    customTypeId?: number;
    smartDispense?: number;

    //是否停用
    disable?: number;
    v2DisableStatus?: GoodsInfoV2DisableStatus; // 停用升级使用字段 0->正常 10-> 20->
    disableSell?: number; // 停售

    //统计相关字段
    profitRat?: number; //毛利率
    totalCost?: number; //药品成本
    recentAvgSell?: number; //日均销量
    turnoverDays?: number; //周转天数

    //社保相关字段
    shebao?: GoodsInfoShebao;
    shebaoPayMode?: number; //社保费用支付方式-强制自费
    shebaoCode?: string;
    medicalFeeGrade?: number; //医保相关

    //判断goods来源
    _isFrom?: number;

    // 商品的大类型
    __type?: number;
    goodsId?: string;

    //药房号
    pharmacyNo?: number;
    pharmacyType?: number;
    pharmacyName?: string;
    //keyId用来匹配是否是同一个药品
    keyId?: string;
    //匹配好的中药名字
    @JsonProperty({ name: "displayName" })
    _displayName?: string;
    //终端自定义存的字段,用来存储传递给后台的medicineCadn
    _medicineCadn?: string;

    // 字段存在于多个药房中才返回
    @JsonProperty({ type: Array, clazz: PharmacyGoodsStockInfo })
    pharmacyGoodsStockList?: PharmacyGoodsStockInfo[];
    /////

    dispGoodsCount?: string; //拼接好的库存字符串,给前端使用
    //门诊开单可售库存量: (总库存 - 禁售库存 - 锁定库存)
    dispStockGoodsCount?: string; //拼接好的库存字符串,给前端使用

    @JsonProperty({ name: "outPieceCount" })
    __apiOutPieceCount?: number;
    @JsonProperty({ name: "outPackageCount" })
    __apiOutPackageCount?: number;
    dispOutGoodsCount?: string;
    prohibitPieceCount?: number; //禁售库存: 过期停售+手动批次停售
    prohibitPackageCount?: number; //禁售库存: 过期停售+手动批次停售
    dispProhibitGoodsCount?: string; //拼接好的库存字符串,给前端使用
    lockingPieceCount?: number; //锁定库存：门诊开单锁库 + 出库调拨锁库
    lockingPackageCount?: number; //锁定库存：门诊开单锁库 + 出库调拨锁库
    dispLockingGoodsCount?: string; //拼接好的库存字符串,给前端使用
    availablePackageCount?: number; //业务可用数量
    availablePieceCount?: number; //业务可用数量

    feeTypeId?: string; // 费用类别
    feeTypeName?: string; // 费用类别
    feeComposeType?: number; //费用类型

    //缺药提示(缺药提示同一个refGoodsName会返回多条GoodsItem)
    // 0条 没找到
    // 1 条 findstatus = 10 11 20 21 按别名或正名精确找到一条哦
    // 1条 findStatus = 30 31 40 41 模糊匹配找到
    // 大于1条 模糊匹配找到)
    findStatus?: number;
    refGoodsName?: string;
    standardName?: string;
    tradeName?: string;
    //精准匹配到一条，直接替换

    baseMedicineType?: BaseMedicineTypeEnum; // 基药类型
    dangerIngredient?: IngredientEnum; // 药品成分位/多选 1:精1 2:精2 4:麻 8:毒 16:放 32:麻黄碱
    dosageFormType?: WesternMedicineDosageFormTypeEnum; // 西药剂型
    maintainType?: MaintainTypeEnum; // 维护类型
    mha?: string; // Marketing Authorization Holder上市许可持有人 默认同厂家
    otcType?: number; // OTC类型 0-非OTC 1-OT
    pharmacologicId?: string; // 药理分类ID
    pharmacologicsName?: string; // 药理分类名称

    priceMakeupPercent?: number; // 售价加成比例 加价50%买，就是50
    priceType?: number; // 1 按进价加成 2 按固定售价
    lockBatchOps?: number; // 是否需要强制锁批次 1--进价加成 2--按批次进行限价
    //  商品按进价加成
    get isPurchasePrice(): boolean {
        return this.lockBatchOps === 1 && this.priceType == 3;
    }
    // 商品按医保锁批次
    get medicalInsuranceLockBatch(): boolean {
        return this.lockBatchOps === 1 && this.priceType !== 3;
    }
    profitCategoryType?: number; // 毛利类型: 1 异常高毛利 2 A类 4 B类 8 C类 16 D类 32 E类
    shelfLife?: number; // 有效期 单位月
    @JsonProperty({ type: Array, clazz: GoodsTagListItem })
    goodsTagList?: GoodsTagListItem[]; // [get]标签
    @JsonProperty({ type: Array, clazz: String })
    goodsTagIdList?: string[]; // [put]标签
    storageType?: number; // 存储类型 存储类型 null无意义 1 常温、2阴凉、4冷藏、8冷冻、16避光
    antibiotic?: AntibioticEnum; // 抗菌药物级别
    dddOfAntibiotic?: number; // 抗菌药物DDD值
    unitOfAntibiotic?: string; // 抗菌药物DDD值单位:μg、mg、g、U、万U、MU
    get getAntibiotic(): AntibioticEnum | (AntibioticEnum | undefined)[] | undefined {
        if (!this.isPackage) {
            return this.antibiotic;
        } else {
            return [...new Set(this.children?.map((item) => item.antibiotic)).values()];
        }
    }
    deviceType?: number;
    feeCategoryId?: string; // 病案首页费目
    gspModifyStatus?: GspModifyStatusEnum; // GSP修改状态
    storage?: string; //存储条件
    @JsonProperty({ type: Array, clazz: BusinessScopeList })
    businessScopeList?: BusinessScopeList[]; //所属经营范围
    @JsonProperty({ fromJson: fromJsonToDate })
    medicineNmpnStartExpiryDate?: Date; //批准问号有效期
    @JsonProperty({ fromJson: fromJsonToDate })
    medicineNmpnEndExpiryDate?: Date; //批准问号有效期
    drugIdentificationCode?: string; // 追溯码 药品唯一标识码
    @JsonProperty({ type: Array, clazz: TraceableCodeNoInfo })
    traceableCodeNoInfoList?: TraceableCodeNoInfo[];
    get _medicineNmpnExpiryDate(): string {
        if (!this.medicineNmpnStartExpiryDate || !this.medicineNmpnEndExpiryDate) return "";
        return `${dateToYyyyMMddString(this.medicineNmpnStartExpiryDate)}-${dateToYyyyMMddString(this.medicineNmpnEndExpiryDate)}`;
    }

    get _businessScopeDisplayName(): string {
        const firstBusinessScope = _.first(this.businessScopeList);
        if (!!firstBusinessScope?.displayName) return firstBusinessScope?.displayName;
        if (!!firstBusinessScope?._displayName) return firstBusinessScope?._displayName;
        return firstBusinessScope?.name ?? "";
    }
    // 终端自定义字段，主要用于药品信息某些必填项未填的提示
    _showTipHint?: boolean;
    subClinicPriceFlag?: number;

    // 是审批中状态
    get isGspWaitVerifyStatus(): boolean {
        return this.gspModifyStatus === GspModifyStatusEnum.waitVerify;
    }
    @JsonProperty({ type: Array, clazz: GoodsBatchInfoList })
    goodsBatchInfoList?: GoodsBatchInfoList[];
    // 自定义，存储外层formItem上的batchInfos
    @JsonProperty({ type: Array, clazz: FormItemBatchInfos })
    _batchInfos?: FormItemBatchInfos[];
    _chargeStatus?: number;

    @JsonProperty({ type: Array, clazz: GoodsMultiPriceView })
    multiPriceList?: GoodsMultiPriceView[]; // 多价格体系-药店-药品折扣信息

    get memberPriceDesp(): string {
        let memberPriceList = this.multiPriceList?.filter((item) => item.targetType == GoodsMultiPriceType.Discount);
        if (!memberPriceList?.length) return "";
        // 如果特价方式为折扣、跟随分类折扣，则需要根据折扣，计算出对应的价格
        // 如果特价方式为折扣、跟随分类折扣，过滤掉discountValue为null的
        memberPriceList = [...memberPriceList]
            ?.filter((item) => !!item.discountValue)
            ?.map((item) => {
                if (item.discountType != MemberPriceDiscountType.SpecialPrice) {
                    item.packagePrice = (this.packagePrice ?? 0) * item.discountValue!;
                }
                return item;
            });

        if (memberPriceList.length > 1) {
            // 如果是折扣类型，过滤packagePrice，其他类型，过滤discountValue
            // 原因：折扣、跟随分类折扣类型中discountValue代表折扣值，而非价格，特价类型中discountValue代表打折后价格
            const prices = memberPriceList
                .map((item) => {
                    if (item.discountType === MemberPriceDiscountType.SpecialPrice) {
                        return item.discountValue ?? 0;
                    } else {
                        return !isNil(item.packagePrice) ? item.packagePrice : undefined;
                    }
                })
                ?.filter((item) => !isNil(item)) as number[];
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);

            return `${abcI18Next.t("¥")}${minPrice.toFixed(2)} - ${abcI18Next.t("¥")}${maxPrice.toFixed(2)}，共${memberPriceList.length}组`;
        } else if (memberPriceList.length === 1) {
            let price = 0;
            if (memberPriceList[0].discountType === MemberPriceDiscountType.SpecialPrice) {
                price = memberPriceList[0].discountValue ?? 0;
            } else {
                price = memberPriceList[0].packagePrice ?? 0;
            }
            return `${abcI18Next.t("¥")}${price.toFixed(2)}`;
        }
        return "";
    }

    clinicPackagePrice?: string;
    clinicPiecePrice?: string;
    maxPackageCostPrice?: number; //进价加成--成本价
    minPackageCostPrice?: number;
    maxPackagePrice?: number;
    minPackagePrice?: number;
    // 进价加成模式---零售价格、拆零价格
    purchasePriceRange(): PurchasePriceRange {
        const obj = {
            maxPackagePrice: this.maxPackagePrice || 0,
            minPackagePrice: this.minPackagePrice || 0,
            maxPiecePrice: 0,
            minPiecePrice: 0,
        };
        if (!_.isNil(this.minPackageCostPrice) && !_.isNil(this.maxPackageCostPrice)) {
            const ratio = 1 + (this.priceMakeupPercent || 0) / 100;
            obj.maxPackagePrice = Number(ABCUtils.paddingMoney(ratio * this.maxPackageCostPrice));
            obj.minPackagePrice = Number(ABCUtils.paddingMoney(ratio * this.minPackageCostPrice));
        }
        if (this.dismounting) {
            obj.maxPiecePrice = Number(ABCUtils.paddingMoney(obj.maxPackagePrice / (this.pieceNum ?? 1)));
            obj.minPiecePrice = Number(ABCUtils.paddingMoney(obj.minPackagePrice / (this.pieceNum ?? 1)));
        }
        return obj;
    }
    // 进价加成-零售价格区间
    get retailPriceRangeStr(): string {
        return `${abcI18Next.t("￥")}${ABCUtils.paddingMoney(this.purchasePriceRange().minPackagePrice ?? 0)}-${abcI18Next.t(
            "￥"
        )}${ABCUtils.paddingMoney(this.purchasePriceRange().maxPackagePrice ?? 0)}${!!this.packageUnit ? "/" + this.packageUnit : ""}`;
    }
    get retailPricePureRange(): string {
        if (this.purchasePriceRange().minPackagePrice == this.purchasePriceRange().maxPackagePrice) {
            return `${ABCUtils.paddingMoney(this.purchasePriceRange().minPackagePrice ?? 0)}`;
        }
        return `${ABCUtils.paddingMoney(this.purchasePriceRange().minPackagePrice ?? 0)}-${ABCUtils.paddingMoney(
            this.purchasePriceRange().maxPackagePrice ?? 0
        )}`;
    }
    //     进价加成-拆零价格区间
    get dismountPriceRangeStr(): string {
        return `${abcI18Next.t("￥")}${ABCUtils.paddingMoney(this.purchasePriceRange().minPiecePrice ?? 0)}-${abcI18Next.t(
            "￥"
        )}${ABCUtils.paddingMoney(this.purchasePriceRange().maxPiecePrice ?? 0)}${!!this.pieceUnit ? "/" + this.pieceUnit : ""}`;
    }

    get _maintainTypeDisplay(): string {
        let str = "";
        switch (this.maintainType) {
            case MaintainTypeEnum.NO:
                str = "无需养护";
                break;
            case MaintainTypeEnum.NORMAL:
                str = "普通养护";
                break;
            case MaintainTypeEnum.VIP:
                str = "重点养护";
                break;
        }
        return str;
    }
    get _storageTypeDisplay(): string {
        let str = "";
        switch (this.storageType) {
            case StorageTypeEnum.NORMAL:
                str = "常温";
                break;
            case StorageTypeEnum.COOL:
                str = "阴凉";
                break;
            case StorageTypeEnum.COLD:
                str = "冷藏";
                break;
            case StorageTypeEnum.FROZEN:
                str = "冷冻";
                break;
            case StorageTypeEnum.DARK:
                str = "避光";
                break;
        }
        return str;
    }

    get _deviceTypeDisplay(): string {
        let str = "";
        switch (this.deviceType) {
            case MedicalMaterialDeviceType.LEVEL_A:
                str = "一类医疗器械";
                break;
            case MedicalMaterialDeviceType.LEVEL_B:
                str = "二类医疗器械";
                break;
            case MedicalMaterialDeviceType.LEVEL_C:
                str = "三类医疗器械";
                break;
        }
        return str;
    }

    get _baseMedicineTypeDisplay(): string {
        let str = "";
        switch (this.baseMedicineType) {
            case BaseMedicineTypeEnum.national:
                str = "国家基药";
                break;
            case BaseMedicineTypeEnum.non:
                str = "非基药";
                break;
            case BaseMedicineTypeEnum.landmarkBased:
                str = "地标基药";
                break;
        }
        return str;
    }

    get _profitCategoryTypeDisplay(): string {
        let str = "";
        switch (this.profitCategoryType) {
            case ProfitCategoryTypeEnum.high:
                str = "异常高毛利";
                break;
            case ProfitCategoryTypeEnum.leve_A:
                str = "A类";
                break;
            case ProfitCategoryTypeEnum.leve_B:
                str = "B类";
                break;
            case ProfitCategoryTypeEnum.leve_C:
                str = "C类";
                break;
            case ProfitCategoryTypeEnum.leve_D:
                str = "D类";
                break;
            case ProfitCategoryTypeEnum.leve_E:
                str = "E类";
                break;
        }
        return str;
    }

    /**
     * 定价模式
     */
    get _priceTypeDisplay(): string {
        let str = "";
        switch (this.priceType) {
            case SubClinicPricePriceMode.purchaseMarkup:
                str = "进价加成";
                break;
            case SubClinicPricePriceMode.sellingPrice:
                str = "固定售价";
                break;
        }
        return str;
    }

    /**
     * 药理作用分类名
     */
    _pharmacologicDisplay(options: { medicineCategory: MedicineCategoryItem[]; pharmacologicId: string }): string {
        const { medicineCategory, pharmacologicId } = options;
        let pharmacologicSourceTypeName = ""; // 主类名
        let pharmacologicSubTypeName = ""; // 次类名

        medicineCategory.map((item) => {
            item.children?.forEach((children) => {
                if (children.id == pharmacologicId) {
                    pharmacologicSourceTypeName = item.category ?? "";
                    pharmacologicSubTypeName = children.category ?? "";
                }
            });
        });
        if (!pharmacologicSourceTypeName && !pharmacologicSubTypeName) return "";
        return `${pharmacologicSourceTypeName}/${pharmacologicSubTypeName}`;
    }

    /**
     *  OTC药物/非处方药 1处方药 2 甲类非处方 4 乙类非处方
     */
    get _otcTypeDisplay(): string {
        let str = "";
        switch (this.otcType) {
            case OtcTypeEnum.prescription:
                str = "处方药";
                break;
            case OtcTypeEnum.CLASS_A_OTC:
                str = "甲类非处方";
                break;
            case OtcTypeEnum.CLASS_B_OTC:
                str = "乙类非处方";
                break;
        }
        return str;
    }

    /**
     * 药品成分位 1:精I 2:精II 4:麻 8:毒 16:放 32:麻黄碱
     */
    get getIngredientArray(): IngredientEnum[] {
        let ingredientList: Iterable<IngredientEnum> | null | undefined = [];
        const ingredientArr = [
            IngredientEnum.JING_1,
            IngredientEnum.JING_2,
            IngredientEnum.MA,
            IngredientEnum.DU,
            IngredientEnum.FANG,
            IngredientEnum.MA_HUANG_JIAN,
        ];
        if (this.isPackage) {
            this.children?.forEach((subGoods) => {
                const ingredient = subGoods.dangerIngredient;
                if (ingredient) {
                    ingredientList = ingredientArr.filter((item) => ingredient & item);
                }
            });
        } else {
            const ingredient = this.dangerIngredient;
            if (ingredient) {
                ingredientList = ingredientArr.filter((item) => ingredient & item);
            }
        }
        return [...new Set(ingredientList).values()];
    }

    /**
     * 通过选中的药品成分位数组 设置药品成分位数值
     * @param ingredientArr
     */
    setIngredientArray(ingredientArr: IngredientEnum[]): number {
        return ingredientArr.reduce((res, item) => res | item, 0);
    }

    /**
     * 药品成分文本显示
     */
    get _ingredientDisplay(): string {
        const str: string[] = [];

        const getIngredientArray = this.getIngredientArray;
        getIngredientArray?.forEach((dangerIngredient) => {
            switch (dangerIngredient) {
                case IngredientEnum.JING_1:
                    str.push("精I");
                    break;
                case IngredientEnum.JING_2:
                    str.push("精II");
                    break;
                case IngredientEnum.MA:
                    str.push("麻");
                    break;
                case IngredientEnum.DU:
                    str.push("毒");
                    break;
                case IngredientEnum.FANG:
                    str.push("放");
                    break;
                case IngredientEnum.MA_HUANG_JIAN:
                    str.push("麻黄碱");
                    break;
            }
        });

        return str.join("、");
    }

    /**
     * 抗菌药物级别
     */
    get _antibioticDisplay(): string {
        let str = "";
        switch (this.antibiotic) {
            case AntibioticEnum.no:
                str = "非限制使用级";
                break;
            case AntibioticEnum.yes:
                str = "限制使用级";
                break;
            case AntibioticEnum.special:
                str = "特殊使用级";
                break;
        }
        return str;
    }
    /**
     * 抗菌药物级别
     */
    get _antibioticListDisplay(): string {
        if (this.getAntibiotic instanceof Array) {
            const str: string[] = [];
            this.getAntibiotic.forEach((item) => {
                switch (item) {
                    case AntibioticEnum.no:
                        str.push("非限制使用级");
                        break;
                    case AntibioticEnum.yes:
                        str.push("限制使用级");
                        break;
                    case AntibioticEnum.special:
                        str.push("特殊使用级");
                        break;
                }
            });
            return str.join("、");
        } else {
            return this._antibioticDisplay;
        }
    }

    get _dosageFormTypeDisplay(): string {
        let str = "";
        switch (this.dosageFormType) {
            case WesternMedicineDosageFormTypeEnum.PIAN:
                str = "片剂";
                break;
            case WesternMedicineDosageFormTypeEnum.WAN:
                str = "丸剂";
                break;
            case WesternMedicineDosageFormTypeEnum.SAN:
                str = "散剂";
                break;
            case WesternMedicineDosageFormTypeEnum.RUANGAO:
                str = "软膏剂";
                break;
            case WesternMedicineDosageFormTypeEnum.RUGAO:
                str = "乳膏剂";
                break;
            case WesternMedicineDosageFormTypeEnum.KE:
                str = "颗粒剂";
                break;
            case WesternMedicineDosageFormTypeEnum.JIAO:
                str = "胶囊剂";
                break;
            case WesternMedicineDosageFormTypeEnum.RONG:
                str = "口服溶液剂";
                break;
            case WesternMedicineDosageFormTypeEnum.HUN:
                str = "口服混悬剂";
                break;
            case WesternMedicineDosageFormTypeEnum.RU:
                str = "口服乳剂";
                break;
            case WesternMedicineDosageFormTypeEnum.ZHU:
                str = "注射剂";
                break;
            case WesternMedicineDosageFormTypeEnum.WU:
                str = "气雾剂";
                break;
            case WesternMedicineDosageFormTypeEnum.XI:
                str = "洗剂";
                break;
            case WesternMedicineDosageFormTypeEnum.CHA:
                str = "搽剂";
                break;
            case WesternMedicineDosageFormTypeEnum.HU:
                str = "糊剂";
                break;
            case WesternMedicineDosageFormTypeEnum.TIE:
                str = "贴剂";
                break;
            case WesternMedicineDosageFormTypeEnum.YAN:
                str = "眼用制剂";
                break;
            case WesternMedicineDosageFormTypeEnum.BI:
                str = "鼻用制剂";
                break;
            case WesternMedicineDosageFormTypeEnum.MO:
                str = "膜剂";
                break;
            case WesternMedicineDosageFormTypeEnum.SHUAN:
                str = "栓剂";
                break;
            case WesternMedicineDosageFormTypeEnum.TANG:
                str = "糖浆剂";
                break;
            case WesternMedicineDosageFormTypeEnum.HUAN:
                str = "缓释制剂";
                break;
            case WesternMedicineDosageFormTypeEnum.KONG:
                str = "控释制剂";
                break;
            case WesternMedicineDosageFormTypeEnum.NING:
                str = "凝胶剂";
                break;
            case WesternMedicineDosageFormTypeEnum.CHONG:
                str = "冲洗剂";
                break;
            case WesternMedicineDosageFormTypeEnum.TU:
                str = "涂剂";
                break;
            case WesternMedicineDosageFormTypeEnum.TUMO:
                str = "涂膜剂";
                break;
            case WesternMedicineDosageFormTypeEnum.DING:
                str = "酊剂";
                break;
            case WesternMedicineDosageFormTypeEnum.FEN:
                str = "粉雾剂";
                break;
            case WesternMedicineDosageFormTypeEnum.PEN:
                str = "喷雾剂";
                break;
            case WesternMedicineDosageFormTypeEnum.XIRU:
                str = "吸入制剂";
                break;
            case WesternMedicineDosageFormTypeEnum.ZHI:
                str = "植入剂";
                break;
            case WesternMedicineDosageFormTypeEnum.JIEYU:
                str = "宫内节育系统";
                break;
            case WesternMedicineDosageFormTypeEnum.SHIFANG:
                str = "宫内释放系统";
                break;
            case WesternMedicineDosageFormTypeEnum.QI:
                str = "其它";
                break;
        }

        return str;
    }

    get isFindOneToReplace(): boolean {
        return this.findStatus === 10 || this.findStatus === 11 || this.findStatus === 20 || this.findStatus === 21;
    }
    //有匹配到其他相似药品，需要提示，不可直接替换
    get isFindMoreGoodsToTip(): boolean {
        return this.findStatus === 30 || this.findStatus === 31 || this.findStatus === 40 || this.findStatus === 41;
    }

    get outPieceCount(): number | undefined {
        //可出库存:(总库存 - 锁定库存) 主要应用与库存模块的进销存操作
        if (!!this.availablePieceCount) {
            return this.availablePieceCount;
        }
        const diffCount =
            (this.packageCount ?? 0) * (this.pieceNum ?? 0) +
            (this.pieceCount ?? 0) -
            ((this.lockingPackageCount ?? 0) * (this.pieceNum ?? 0) + (this.lockingPieceCount ?? 0));
        return diffCount % (this.pieceNum ?? 0);
    }

    get outPackageCount(): number {
        //可出库存:(总库存 - 锁定库存) 主要应用与库存模块的进销存操作;
        if (!!this.availablePackageCount) {
            return this.availablePackageCount;
        }

        if (this.isChineseMedicine) {
            return 0;
        }

        const diffCount =
            (this.packageCount ?? 0) * (this.pieceNum ?? 0) +
            (this.pieceCount ?? 0) -
            ((this.lockingPackageCount ?? 0) * (this.pieceNum ?? 0) + (this.lockingPieceCount ?? 0));

        return Math.floor(diffCount / (this.pieceNum ?? 1));
    }

    get __outPackageCount(): number {
        if (!!this.__apiOutPackageCount) {
            return this.__apiOutPackageCount;
        }

        if (this.isChineseMedicine) {
            return 0;
        }

        const diffCount =
            (this.packageCount ?? 0) * (this.pieceNum ?? 0) +
            (this.pieceCount ?? 0) -
            ((this.lockingPackageCount ?? 0) * (this.pieceNum ?? 0) + (this.lockingPieceCount ?? 0));

        return Math.floor(diffCount / (this.pieceNum ?? 1));
    }

    get __outPieceCount(): number | undefined {
        //可出库存:(总库存 - 锁定库存) 主要应用与库存模块的进销存操作
        if (!!this.__apiOutPieceCount) {
            return this.__apiOutPieceCount;
        }
        const diffCount =
            (this.packageCount ?? 0) * (this.pieceNum ?? 0) +
            (this.pieceCount ?? 0) -
            ((this.lockingPackageCount ?? 0) * (this.pieceNum ?? 0) + (this.lockingPieceCount ?? 0));
        return diffCount % (this.pieceNum ?? 0);
    }

    get _outTotalCountDisplay(): string {
        let str = "";
        if (!!this.__outPackageCount) {
            str += `${this.__outPackageCount}${this.packageUnit}`;
        }

        if (!!this.__outPieceCount) {
            str += `${this.__outPieceCount}${this.pieceUnit}`;
        }
        return str;
    }
    /////

    shebaoNationalCode?: string; // 医保对码
    shebaoNationalView?: ShebaoNationalView; // 医保对码

    get displaySheBaoPayModeName(): string {
        let sheBaoPayModeName = "";
        switch (this.shebaoPayMode) {
            case SheBaoPayModeEnum.OVERALL:
                sheBaoPayModeName = "优先统筹支付";
                break;
            case SheBaoPayModeEnum.SELF:
                sheBaoPayModeName = "优先个账支付";
                break;
            case SheBaoPayModeEnum.NO_USE:
                sheBaoPayModeName = "不使用医保支付";
                break;
        }

        return sheBaoPayModeName;
    }

    get displayManufacturer(): string {
        return this.manufacturer ?? this.manufacturerFull ?? "";
    }

    get packageChildrenDisplay(): string {
        const _displayName: string[] = [];
        if (!this.children || _.isEmpty(this.children)) return "";
        this.children.forEach((item) => {
            let composePackageInfo: string;
            if (item.composeUseDismounting) {
                composePackageInfo = `${item.composePieceCount}${item.pieceUnit}`;
            } else {
                composePackageInfo = `${item.composePackageCount}${item.packageUnit}`;
            }
            _displayName.push(`${item.displayName}(${composePackageInfo})`);
        });
        return _displayName.join("、");
    }

    compareKey(): string {
        //keyId是唯一的标识
        if (!!this.keyId) return this.keyId;
        //同一个药品，id可能是不一样的
        // if (!_.isEmpty(this.id)) return this.id!;
        //
        // if (!_.isEmpty(this.medicineCadn)) return this.medicineCadn!;

        return this.scrollKey;
    }

    public equals(goods: GoodsInfo): boolean {
        if (!_.isEmpty(this.id) || !_.isEmpty(goods.id)) return this.id === goods.id;

        return this.medicineCadn === goods.medicineCadn;
    }

    //滚动使用

    _scrollKey?: string;
    get scrollKey(): string {
        if (!this._scrollKey) {
            this._scrollKey = this.id + this.displayName + Math.random();
        }
        return this._scrollKey;
    }

    // @enumerable(false)
    getStockPieceCount(): number | undefined {
        return this.stockPieceCount ?? this.pieceCount ?? 0;
    }

    getStockPackageCount(): number {
        return this.stockPackageCount ?? this.packageCount ?? 0.0;
    }

    //计算库存量，如果可拆零，则转成piece量
    get stockCountWithPieceUnitPrefer(): number {
        if (this.canDismounting) {
            return (this.getStockPackageCount() || 0) * (this.pieceNum || 0) + (this.getStockPieceCount() || 0);
        }

        return this.getStockPackageCount();
    }

    //是否有库存，可以进行销售
    get hasStock(): boolean {
        const type = this.type;
        return (
            type == GoodsType.treatment ||
            type == GoodsType.examination ||
            type == GoodsType.otherGoods49 ||
            type == GoodsType.nurseProduct ||
            this.isPackage ||
            (this.getStockPackageCount() || 0) > 0 ||
            (this.getStockPieceCount() || 0) > 0
        );
    }

    //库存模块是否有库存，，可以进行销售
    get hasInventoryStock(): boolean {
        const type = this.type;
        return (
            type == GoodsType.treatment ||
            type == GoodsType.examination ||
            type == GoodsType.otherGoods49 ||
            this.isPackage ||
            (this.packageCount || 0) > 0 ||
            (this.pieceCount || 0) > 0
        );
    }

    get inStock(): boolean {
        const type = this.type;
        const noStocks = this.noStocks;
        return (
            Boolean(this.id) &&
            (noStocks == null ||
                !noStocks ||
                type == GoodsType.treatment ||
                type == GoodsType.examination ||
                type == GoodsType.otherGoods49 ||
                type == GoodsType.nurseProduct ||
                type == GoodsType.surgery ||
                this.isPackage)
        );
    }

    stockCountWithUnit(unit: string): number {
        LogUtils.d("stockCountWithUnit unit = $unit, type = $type");
        //治疗，理疗，库存无限大
        const type = this.type;
        if (
            type == GoodsType.deliveryFee ||
            type == GoodsType.decoctionFee ||
            type == GoodsType.treatment ||
            type == GoodsType.examination ||
            type == GoodsType.otherGoods49 ||
            type == GoodsType.surgery ||
            this.isPackage
        )
            return Infinity;

        if (this.canDismounting) {
            if (unit == this.pieceUnit) return this.stockCountWithPieceUnitPrefer;
            else return this.getStockPackageCount();
        } else return this.getStockPackageCount();
    }

    get displayCMSpec(): string {
        const prefix = "中药";
        const cMSpec = this.cMSpec;
        if (cMSpec != null && cMSpec.startsWith(prefix)) return cMSpec.substring(cMSpec.indexOf(prefix) + prefix.length);
        if (cMSpec != null) return cMSpec || "";

        const materialSpec = this.materialSpec;
        if (materialSpec != null && materialSpec.startsWith(prefix))
            return materialSpec.substring(materialSpec.indexOf(prefix) + prefix.length);
        return materialSpec || "";
    }

    //在销售时可用单位列表
    get sellUnits(): Array<string> {
        if (this.isChineseMedicine) return [this.pieceUnit!]; //中药只有小单位

        const units = new Array<string>();
        if (this.canDismounting) {
            if (this.pieceUnit) units.push(this.pieceUnit);

            if (this.packageUnit) units.push(this.packageUnit);

            if (!_.isEmpty(units)) return units;
        }

        if (this.packageUnit) return [this.packageUnit];
        if (this.pieceUnit) return [this.pieceUnit];

        if (this.isTreatment || this.isExamination || this.isExaminationTest || this.isPackage || this.isPhysiotherapy) {
            return ["次"];
        }

        return [];
    }

    //使用单位

    get usageUnits(): Array<string> {
        const units = new Array<string>();
        if (this.medicineDosageUnit && !_.isNil(this.medicineDosageUnit)) {
            units.push(this.medicineDosageUnit);
        }

        if (this.pieceUnit && this.medicineDosageUnit != this.pieceUnit) {
            units.push(this.pieceUnit);
        }

        if (
            !!this.componentContentUnit &&
            this.componentContentUnit != this.medicineDosageUnit &&
            this.componentContentUnit != this.pieceUnit
        ) {
            units.push(this.componentContentUnit);
        }

        return units;
    }

    get stockInUnits(): Array<string> {
        if (this.isChineseMedicine) {
            const units = [this.pieceUnit!]; //中药只有小单位
            if (this.pieceUnit == "g") units.push("Kg");
            return units;
        }

        const units = new Array<string>();
        if (this.canDismounting) {
            if (this.packageUnit) units.push(this.packageUnit);
            if (this.pieceUnit) units.push(this.pieceUnit);
            return units;
        }

        if (this.packageUnit) return [this.packageUnit];
        if (this.pieceUnit) return [this.pieceUnit];

        return ["次"];
    }

    /**
     * @description 可以使用的单位
     * 不区分是否可拆零
     */
    get canUseUnits(): Array<string> {
        if (this.isChineseMedicine) {
            //中药只有小单位
            // if (this.pieceUnit == "g") units.push("Kg");
            return [this.pieceUnit!];
        }

        const units = new Array<string>();
        if (this.packageUnit) units.push(this.packageUnit);
        if (this.pieceUnit && this.pieceUnit != this.packageUnit) units.push(this.pieceUnit);
        return units;
    }

    //可以销售的数量
    canSellCountWithUnit(unit: string): number | undefined {
        const toStockPieceCount = (this.getStockPieceCount() || 0.0) + (this.getStockPackageCount() || 0.0) * (this.pieceNum || 0);

        if (unit == this.pieceUnit) return toStockPieceCount;
        else if (unit == this.packageUnit) return this.getStockPackageCount();
    }

    get unitPreferPackage(): string {
        if (this.packageUnit) return this.packageUnit;
        if (this.pieceUnit) return this.pieceUnit;
        if (this.isChineseMedicine) return "g";

        return "次";
    }

    get unitPricePreferPackage(): number {
        return this.unitPriceWithUnit(this.unitPreferPackage);
    }

    get unit(): string {
        if (!_.isEmpty(this.pieceUnit)) return this.pieceUnit!;
        else if (!_.isEmpty(this.packageUnit)) return this.packageUnit!;
        if (this.isChineseMedicine) return "g";
        return "次";
    }

    get unitPrice(): number {
        return this.unit && this.unit == this.pieceUnit ? this.piecePrice! : this.packagePrice!;
    }

    unitPriceWithUnit(unit: string): number {
        let price: number;
        if (
            this.isTreatment ||
            this.isExamination ||
            this.isExaminationTest ||
            this.isPackage ||
            this.isPhysiotherapy ||
            this.isTreatmentOther
        ) {
            price = this.packagePrice!;
            return price;
        }
        if (this.canDismounting) {
            if (unit == this.packageUnit) price = this.packagePrice || 0.0;
            else price = this.piecePrice || 0.0;
        } else price = this.packagePrice || 0.0;

        return price;
    }

    useDismountingWithUnit(unit: string): boolean {
        return this.canDismounting && unit == this.pieceUnit;
    }

    // 药品
    get isMedicine(): boolean {
        return this.type == GoodsType.medicine;
    }

    // 西药
    get isWesternMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineWestern == this.subType;
    }

    // 中成药
    get isChineseWesternMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineChinesePatent == this.subType;
    }

    // 中药
    get isChineseMedicine(): boolean {
        return this.type == GoodsType.medicine && GoodsSubType.medicineChinese == this.subType;
    }

    // 治疗
    get isTreatment(): boolean {
        return this.type == GoodsType.treatment && GoodsSubType.treatment == this.subType;
    }

    // 理疗
    get isPhysiotherapy(): boolean {
        return this.type == GoodsType.treatment && GoodsSubType.treatmentPhysiotherapy == this.subType;
    }

    // 是中药饮片类型
    get isMedicineChinesePiece(): boolean {
        return this.typeId == GoodsTypeId.medicineChinesePiece && this.subType == GoodsSubType.medicineChinese;
    }

    // 是非配方饮片类型
    get isMedicineChineseNonFormula(): boolean {
        return this.typeId == GoodsTypeId.medicineChineseNonFormula && this.subType == GoodsSubType.medicineChinese;
    }

    // 是中药颗粒类型
    get isMedicineChineseGranule(): boolean {
        return this.typeId == GoodsTypeId.medicineChineseGranule && this.subType == GoodsSubType.medicineChinese;
    }

    // 是西药、中成药类型
    get isMedicineWestAndChinesePatent(): boolean {
        return this.isWesternMedicine || this.isChineseWesternMedicine;
    }

    // 治疗理疗-其它
    get isTreatmentOther(): boolean {
        return this.type == GoodsType.treatment && GoodsSubType.treatmentOther == this.subType;
    }

    // 检查检验-检验
    get isExamination(): boolean {
        return this.type == GoodsType.examination && GoodsSubType.examination == this.subType;
    }

    // 检查检验-检查
    get isExaminationTest(): boolean {
        return this.type == GoodsType.examination && GoodsSubType.examinationTest == this.subType;
    }

    /// 物资
    get isMaterial(): boolean {
        return this.type == GoodsType.material;
    }

    // 医疗器械
    get isMedicalMaterial(): boolean {
        return (
            this.type == GoodsType.material && this.typeId == GoodsTypeId.materialMedical && GoodsSubType.materialMedical == this.subType
        );
    }

    // 消毒用品
    get isMaterialDisinfectant(): boolean {
        return this.typeId == GoodsTypeId.materialDisinfectant && GoodsSubType.materialDisinfectant == this.subType;
    }

    // 化妆品
    get isCosmetics(): boolean {
        return this.typeId == GoodsTypeId.cosmetics && GoodsSubType.cosmetics == this.subType;
    }

    // 后勤材料
    get isMaterialLogistics(): boolean {
        return (
            this.type == GoodsType.material && this.typeId == GoodsTypeId.materialLogistics && GoodsSubType.materialMedical == this.subType
        );
    }

    // 固定资产
    get isMaterialFixedAssets(): boolean {
        return (
            this.type == GoodsType.material &&
            this.typeId == GoodsTypeId.materialFixedAssets &&
            GoodsSubType.materialMedical == this.subType
        );
    }

    /// 商品
    get isGoods(): boolean {
        return this.type == GoodsType.goods;
    }

    // 自制成品
    get isGoodsHomemade(): boolean {
        return this.type == GoodsType.goods && this.typeId == GoodsTypeId.goodsHomemade && GoodsSubType.goodsHomemade == this.subType;
    }

    // 保健药品
    get isHealthMedicine(): boolean {
        return this.type == GoodsType.goods && this.typeId == GoodsTypeId.healthMedicine && GoodsSubType.healthMedicine == this.subType;
    }

    // 保健食品
    get isHealthGood(): boolean {
        return this.type == GoodsType.goods && this.typeId == GoodsTypeId.healthGood && GoodsSubType.healthGood == this.subType;
    }

    // 其他商品
    get isOtherGood(): boolean {
        return this.type == GoodsType.goods && this.typeId == GoodsTypeId.otherGood && GoodsSubType.otherGood == this.subType;
    }

    // 套餐
    get isPackage(): boolean {
        // return this.type == GoodsType.package && GoodsSubType.packageTreatment == this.subType;
        return this.type == GoodsType.package;
    }

    // 其他费用
    get isOtherFee(): boolean {
        return this.type == GoodsType.otherGoods49;
    }

    get isGlasses(): boolean {
        return this.type == GoodsType.glasses;
    }

    get isNurseFee(): boolean {
        return this.type == GoodsType.nurseProduct;
    }

    get isSurgery(): boolean {
        return this.type == GoodsType.surgery;
    }

    get isNotTreatment(): boolean {
        return this.type != GoodsType.treatment;
    }

    /**
     * 当前药品是否支持修改商品来源
     */
    get isCanSpecifyPharmacy(): boolean {
        return !(
            this.isExaminationTest ||
            this.isTreatmentOther ||
            this.isTreatment ||
            this.isPhysiotherapy ||
            this.isExamination ||
            this.isPackage ||
            this.isNurseFee ||
            this.isOtherFee ||
            this.isSurgery
        );
    }
    get isDrugstoreButler(): boolean {
        return _isDrugstoreButlerCallback();
    }

    get displayTypeName(): string | undefined {
        if (this.isWesternMedicine) return "西药";
        else if (this.isChineseWesternMedicine) return "中成药";
        else if (this.isMedicineChinesePiece && !this.isDrugstoreButler) return "中药饮片";
        else if (this.isMedicineChinesePiece && this.isDrugstoreButler) return "配方饮片";
        else if (this.isMedicineChineseNonFormula) return "非配方饮片";
        else if (this.isMedicineChineseGranule) return "中药颗粒";
        else if (this.isMedicalMaterial) return "医疗器械";
        else if (this.isMaterialLogistics) return "后勤材料";
        else if (this.isMaterialFixedAssets) return "固定资产";
        else if (this.isGoodsHomemade) return "自制成品";
        else if (this.isHealthMedicine) return "保健药品";
        else if (this.isHealthGood) return "保健食品";
        else if (this.isOtherGood) return "其他商品";
        else if (this.isTreatment) return "治疗";
        else if (this.isPhysiotherapy) return "理疗";
        else if (this.isTreatmentOther) return "治疗理疗其他";
        else if (this.isExamination) return "检验";
        else if (this.isExaminationTest) return "检查";
        else if (this.isPackage) return "套餐";
        else if (this.isNurseFee) return "护理";
        else if (this.isSurgery) return "手术";
        else {
            let filter;
            if (this.typeId) {
                filter = goodsTypeIds.find((item) => item.id == this.typeId);
            } else {
                filter = goodsTypeIds.find((item) => item.type == this.type && item.subType == this.subType && item.cMSpec == this.cMSpec);
            }
            if (filter) {
                return filter.label;
            }
            if (this.isGoods) {
                return "商品";
            }
        }
    }

    /**
     * 诊疗项目需要简称
     */
    get displayTypeShortNameV2(): string | undefined {
        if (this.isTreatment) return "治疗";
        else if (this.isPhysiotherapy) return "理疗";
        else if (this.type == GoodsType.medicine) return "药品";
        else if (this.type == GoodsType.otherGoods49) return "其他";
        else if (this.type == GoodsType.material) return "物资";
        else return "商品";
    }

    /**
     * 诊疗项目需要简称
     */
    get displayTypeShortName(): string | undefined {
        if (this.isWesternMedicine) return "西药";
        else if (this.isChineseWesternMedicine) return "中成";
        else if (this.isTreatment) return "治疗";
        else if (this.isPhysiotherapy) return "理疗";
        else if (this.isTreatmentOther) return "其他";
        else if (this.isExamination) return "检验";
        else if (this.isExaminationTest) return "检查";
        else if (this.isMedicalMaterial) return "物资";
        else if (this.isNurseFee) return "护理";
        else if (this.isPackage) return "套餐";
        else if (this.isSurgery) return "手术";
        else {
            let filter;
            if (this.typeId) {
                filter = goodsTypeIds.find((item) => item.id == this.typeId);
            } else {
                filter = goodsTypeIds.find((item) => item.type == this.type && item.subType == this.subType && item.cMSpec == this.cMSpec);
            }
            if (filter) {
                return filter.label;
            }
            if (this.isGoods) {
                return "商品";
            }
        }
    }

    get displayName(): string {
        if (!!this._displayName) return this._displayName.trim();
        if (this.isChineseMedicine) {
            if (this.medicineCadn) return this.medicineCadn.trim();

            if (this.name) return this.name.trim();
            return "";
        }

        let fullName = "";
        if (this.medicineCadn) {
            fullName = this.medicineCadn;
        }

        if (this.name) {
            if (!!fullName) {
                fullName += `(${this.name})`;
            } else fullName = this.name;
        }

        return fullName;
    }

    //药理类型默认显示 PharmacologicSourceType
    displayPharmacologicSource(): { pharmacologicName: string; pharmacologicSourceType: number } {
        let displayPharmacologic = { pharmacologicName: "", pharmacologicSourceType: 1 };

        if (this.isWesternMedicine) {
            displayPharmacologic = { pharmacologicName: "药理分类", pharmacologicSourceType: 1 };
        } else if (this.isChineseWesternMedicine) {
            displayPharmacologic = { pharmacologicName: "科室分类", pharmacologicSourceType: 2 };
        } else if (this.isChineseMedicine) {
            displayPharmacologic = { pharmacologicName: "功效分类", pharmacologicSourceType: 3 };
        }

        return displayPharmacologic;
    }

    //打包规格
    get packageSpec(): string {
        if (!!this.displaySpec) {
            return this.displaySpec;
        }

        if (this.isWesternMedicine || this.isChineseWesternMedicine) {
            let dosageSpec = "";
            if (this.medicineDosageNum != null && this.medicineDosageUnit) {
                dosageSpec = `${this.medicineDosageNum}${this.medicineDosageUnit}*`;
            }

            if (this.pieceNum && this.pieceUnit && this.packageUnit)
                return `${dosageSpec}${NumberUtils.formatMaxFixed(this.pieceNum || 0)}${this.pieceUnit}/${this.packageUnit}`;

            return "";
        }

        if (this.isGoods || this.isMedicalMaterial) {
            let dosageSpec = "";
            if (this.materialSpec) {
                dosageSpec = `${this.materialSpec}*`;
            }

            return `${dosageSpec}${NumberUtils.formatMaxFixed(this.pieceNum || 0)}${this.pieceUnit}/${this.packageUnit}`;
        }

        if (this.isChineseMedicine) {
            return this.extendSpec ?? this.pieceUnit ?? "";
        }

        return "";
    }

    get priceSpec(): string {
        if (this.packageUnit) return `${abcI18Next.t("¥")}${NumberUtils.formatMaxFixed(this.packagePrice, 2)}/${this.packageUnit}`;
        if (this.pieceUnit) return `${abcI18Next.t("¥")}${ABCUtils.formatPrice(this.packagePrice!)}/${this.pieceUnit}`;

        return `${abcI18Next.t("¥")}${ABCUtils.formatPrice(this.packagePrice!)}/次`;
    }

    priceSpecWithUnit(unit: string): string {
        const priceStr = NumberUtils.formatMaxFixed(this.unitPriceWithUnit(unit), 2);

        return `${abcI18Next.t("¥")}${priceStr}/${unit || "次"}`;
    }

    //是否可以拆零

    get canDismounting(): boolean {
        return this.dismounting == 1;
    }

    get getRecentAvgSell(): string {
        if (_.isUndefined(this.recentAvgSell)) return "";
        const unit = this.isChineseMedicine ? this.pieceUnit : this.packageUnit;
        return `${this.recentAvgSell}${unit}`;
    }

    useDismounting(unit: string): boolean {
        LogUtils.d(`GoodsInfo.useDismounting canDismounting = ${this.canDismounting}, unit = ${unit}, pieceUnit=${this.pieceUnit} `);
        return this.canDismounting && unit == this.pieceUnit;
    }

    /**
     * 拼接剩余库存信息, e.g: 余30盒5片
     * isInventory--是否是库存模块（库存模块那边 用pacakgeCount/pieceCount
     * 门诊开放那里用stockPacakgeCount /stockPieceCount）
     */
    public displayStockInfo(isInventory?: boolean, canOut = false): string {
        if (this.isPackage || this.isTreatment || this.isExamination) return "";

        let stock = "";
        let packageCount = 0,
            pieceCount;
        if (!!isInventory) {
            packageCount = (canOut ? this.outPackageCount : undefined) ?? this.packageCount ?? 0;
            pieceCount = (canOut ? this.outPieceCount : undefined) ?? this.pieceCount ?? 0;
        } else {
            packageCount = this.getStockPackageCount() ?? 0;
            pieceCount = this.getStockPieceCount() ?? 0;
        }

        if (packageCount != 0) {
            stock = `${packageCount}${this.packageUnit}`;
        }

        if (pieceCount != 0) {
            stock = `${stock}${Math.abs(pieceCount)}${this.pieceUnit}`;
        }

        //没有库存
        if (_.isEmpty(stock)) {
            stock = `0${this.unitPreferPackage}`;
        }

        return stock;
    }

    /**
     * 用于计算套餐里的子项的库存信息
     * @param unitCount
     * @param doseCount
     */
    stockInfoForPackageItem(unitCount?: number, doseCount?: number): StockInfo {
        const composeUseDismounting = this.composeUseDismounting == 1;
        let itemUnitCount = unitCount ?? 1;
        if (itemUnitCount !== undefined) {
            itemUnitCount = (doseCount ?? 1) * itemUnitCount;
        }

        return this.stockInfo(
            composeUseDismounting ? this.pieceUnit : this.packageUnit,
            composeUseDismounting ? this.composePieceCount : this.composePackageCount,
            itemUnitCount,
            undefined,
            undefined,
            ""
        );
    }

    //根据传入的用量和当前库存做对比
    stockInfo(
        unit: string | undefined,
        unitCount: number | undefined,
        doseCount: number | undefined,
        overrideStockPieceCount?: number,
        overrideStockPackageCount?: number,
        prefix = "库存不足，",
        pharmacyType?: number,
        batchList?: DispensingFormItemBatch[]
    ): StockInfo {
        const type = this.type;
        //治疗，理疗，不考虑库存
        //虚拟药房也不考虑库存
        if (
            type == GoodsType.treatment ||
            type == GoodsType.examination ||
            type == GoodsType.registration ||
            type == GoodsType.deliveryFee ||
            type == GoodsType.decoctionFee ||
            type == GoodsType.ingredient ||
            type == GoodsType.nurseProduct ||
            type == GoodsType.otherGoods49 ||
            type == GoodsType.surgery
        ) {
            return new StockInfo(true);
        }

        if (!!this.disableSell) return new StockInfo(false, "已停用不可销售");

        if (!this.inStock) return new StockInfo(false, "不在库存中");

        if (this.isPackage) {
            let stockEnough = true;
            let stockTips = "";
            this.children?.forEach((item) => {
                let itemUnitCount = unitCount;
                if (itemUnitCount !== undefined) {
                    itemUnitCount = (doseCount ?? 1) * itemUnitCount;
                }
                const composeUseDismounting = item.composeUseDismounting == 1;
                const stockInfo = item.stockInfo(
                    composeUseDismounting ? item.pieceUnit : item.packageUnit,
                    composeUseDismounting ? item.composePieceCount : item.composePackageCount,
                    itemUnitCount,
                    undefined,
                    undefined,
                    ""
                );
                if (!stockInfo.stockEnough) {
                    stockEnough = false;
                    if (stockTips.length > 0) stockTips += "\n";
                    stockTips += `${item.displayName} ${stockInfo.tips}`;
                }
            });

            return new StockInfo(stockEnough, `${prefix}${stockTips}`);
        }

        if (unitCount == null || doseCount == null) return new StockInfo(true);

        if (pharmacyType == 2 && !!this.id) {
            return new StockInfo(true);
        }
        // 是否按照锁批次的方式锁库，如果是按批次锁库，判断存在按批次锁库的库存不足(目前业务上只针对药房模块)
        if (this?.lockBatchOps == 1 && !!batchList?.length) {
            const existBatchNotEnough = batchList?.some((t) => {
                const waitDispensingAmount = t.unitCount ?? 0,
                    deductibleInventory = (unit == this?.packageUnit ? t.batchInfo?.cutPackageCount : t.batchInfo?.cutPieceCount) ?? 0;
                return waitDispensingAmount > deductibleInventory;
            });
            if (existBatchNotEnough) {
                return new StockInfo(false, BATCH_LOCKING_METHOD);
            } else {
                return new StockInfo(true);
            }
        }

        //    LogUtils.info('stockInfo unit=${unit}, unitCount = ${unitCount}, doseCount =${doseCount}');
        const stockPieceCount = overrideStockPieceCount || this.getStockPieceCount() || 0;
        const stockPackageCount = overrideStockPackageCount || this.getStockPackageCount() || 0;

        //    LogUtils.info('stockPieceCount=$stockPieceCount, stockPackageCount = $stockPackageCount');
        const pieceUnit = this.pieceUnit || "";
        const packageUnit = this.packageUnit || "";

        const pieceNum = defaultIfNil(this.pieceNum, 1);

        const useDismounting = (this.dismounting || 0) > 0 && this.pieceUnit == unit && unit != this.packageUnit;

        const count = unitCount * defaultIfNil(doseCount, 1.0);
        let totalStock: number;

        if (useDismounting) {
            totalStock = pieceNum * stockPackageCount + stockPieceCount;
        }
        // 中药库存直接是stockPieceCount
        else if (this.subType == 2 && this.type == 1) {
            totalStock = stockPieceCount;
        } else {
            totalStock = stockPackageCount;
        }

        let tips;
        if (count > totalStock) {
            let packageCountStr = "",
                pieceCountStr = "";
            if (stockPackageCount > 0 || this.packageUnit) {
                packageCountStr = `${_.floor(stockPackageCount)}${packageUnit}`;
            }
            if (stockPieceCount > 0 || pieceUnit) {
                pieceCountStr = `${_.floor(stockPieceCount, 2)}${pieceUnit}`;
            }
            tips = `${prefix}剩余${packageCountStr}${pieceCountStr}`;
        } else {
            tips = "";
        }

        return new StockInfo(count <= totalStock, tips);
    }

    resetPrice(): void {
        //@ts-ignore
        this.packagePrice = undefined;
        //@ts-ignore
        this.piecePrice = "";
    }

    get isAllowShebaoPay(): boolean {
        return !!this.shebaoCode || !!this.shebaoCode;
    }

    //医保相关的判断
    get medicalFeeGrade2Str(): string {
        switch (this.medicalFeeGrade) {
            case 1:
                return "甲";
            case 2:
                return "乙";
            case 3:
                return "丙";
            case 4:
                return "医保";
            default:
                return "";
        }
    }
    get isCoItemPharmacy(): boolean {
        return this.pharmacyType == 20;
    }
}
export class TraceableCodeList {
    batchId?: string;
    batchNo?: string;
    id?: string;
    no?: string;
    @JsonProperty({ type: TraceableCodeNoInfo })
    traceableCodeNoInfo?: TraceableCodeNoInfo;
    used?: number;
    drugIdentificationCode?: string;
    traceCode?: string;
    traceableCodeType?: number;
    type?: number;
    @JsonProperty({ type: GoodsInfo })
    goodsInfo?: GoodsInfo;
    flag?: any;
    serialNumber?: string;
    pieceCount?: number;
    count?: number;
}
export class Age {
    year?: number;
    month?: number;
    day?: number;

    /**
     * 年龄展示规则全局调整为
     * <0岁0月n天，展示n天>
     * <0岁n月n天，展示n月n天>
     * <n(n＜12)岁n月n天，展示n岁n月>
     * <n(n≥12)岁n月n天，展示n岁>
     */
    get displayAge(): string {
        let str = "";
        const ageReachedTwelveYearsOld = !_.isNil(this.year) && this.year >= 12; // 年龄已满12岁
        const hasYear = !_.isNil(this.year) && this.year > 0;
        const hasMonth = !_.isNil(this.month) && this.month > 0;
        const hasDay = !_.isNil(this.day) && this.day > 0;

        if ((hasYear && !ageReachedTwelveYearsOld) || ageReachedTwelveYearsOld) str += `${this.year}岁`;
        if (hasMonth && !ageReachedTwelveYearsOld) str += `${this.month}月`;
        if (hasDay && this.year === 0) str += `${this.day}天`;

        if (this.year === 0 && this.month === 0 && this.day === 0) str = `1天`;

        return str;
    }

    get isValid(): boolean {
        return !_.isNil(this.year) || !_.isNil(this.month) || !_.isNil(this.day);
    }

    get displayAgeIncludeMonth(): string {
        let str = "";

        if (this.year != null && this.year > 0) str = `${this.year}岁`;

        if (this.month != null && this.month > 0) str = str + `${this.month}月`;

        if (_.isEmpty(str) && this.day != null && this.day > 0) str = `${this.day}天`;

        if (_.isEmpty(str) && this.year === 0 && this.month === 0) str = `0岁`;

        return str;
    }

    get AgeToPoint(): number {
        let _number = 0;
        _number += this.year ?? 0;
        _number += (this.month ?? 0) / 12;

        return Number(_number.toFixed(2));
    }

    /**
     * 判断当前患者是否是低于年龄
     * @param year
     * @example {year:5}.validLimitYear() => return true
     * @example {year:10}.validLimitYear() => return false
     */
    validLimitYear(year = 6): boolean {
        return this.isValid && (this.year ?? 0) <= year;
    }
}

export class PatientAddress {
    addressCityId?: string;
    addressCityName?: string;
    addressDetail?: string;
    addressDistrictId?: string;
    addressDistrictName?: string;
    addressGeo?: string;
    addressProvinceId?: string;
    addressProvinceName?: string;

    get addressDisplay(): string {
        return `${this.addressProvinceName ?? ""}${this.addressCityName ?? ""}${this.addressDistrictName ?? ""}${this.addressDetail ?? ""}`;
    }
    get addressDisplayWithGeo(): string {
        return `${this.addressProvinceName ?? ""}${this.addressCityName ?? ""}${this.addressDistrictName ?? ""}`;
    }
}

function fromJSonMemberInfo(json: any): MemberInfo {
    return JsonMapper.deserialize(MemberInfo, json);
}

export enum WxBindStatus {
    NO_SUBSCRIBE, // 未关注
    SUBSCRIBE_AND_NO_BIND, // 关注未绑定
    BIND_AND_NO_SUBSCRIBE, // 绑定 未关注
    SUBSCRIBE_AND_BIND, // 关注且绑定
}

export interface PatientTag {
    tagId?: string;
    tagName?: string;
    tagType?: string;
    status?: number;

    viewMode?: number; //展示类型，0:文字;1:图标
    editPermit?: number;
    genMode?: number;
    patientId?: number;
    style?: {
        color: string;
        iconUrl?: string;
        shape?: string;
        text: string;
        viewMode?: number;
    };
}

export class PatientSource {
    id?: string;
    name?: string; // 来源方式
    sourceFrom?: string;
    sourceFromName?: string; // 患者来源-姓名
    status?: number;
    relatedType?: number; // 关联类型

    get sourceDisplay(): string {
        if (!this.id?.length) return "";

        let display = `${this.name}`;
        if (this.sourceFromName) {
            display = `${display} - ${this.sourceFromName}`;
        }
        return display;
    }
}

// 查询来源分类列表
export interface PatientsSourceTypesItem {
    id?: string;
    name?: string;
    level?: number;
    parentId?: string;
    children?: PatientsSourceTypesItem[];

    namePy?: string;
    namePyFirst?: string;
    relatedType?: number;
}

export enum CardType {
    sfz = 1,
}

export class Patient {
    id?: string;
    name?: string;
    namePy?: string;
    namePyFirst?: string;

    birthday?: string;

    @JsonProperty({ fromJson: fromJsonToDate })
    ActiveDate?: Date;

    countryCode?: string; // 国家编码
    mobile?: string;
    sex?: string = "男";

    @JsonProperty({ type: Age })
    age?: Age;
    wxOpenId?: string;
    isMember?: number;

    @JsonProperty({ fromJson: fromJSonMemberInfo })
    memberInfo?: MemberInfo;

    clinicId?: string;

    //  patientId;
    chainId?: string;
    activeClinicId?: string;
    clinicName?: string;
    lastModified?: string;

    wxNickName?: string;
    wxHeadImgUrl?: string;
    isAttention?: number; //是否关注微信公众号

    @JsonProperty({ type: PatientAddress })
    address?: PatientAddress;
    addressCityId?: string;
    addressCityName?: string;
    addressDetail?: string;
    addressDistrictId?: string;
    addressDistrictName?: string;
    addressGeo?: string;
    addressProvinceId?: string;
    addressProvinceName?: string;
    company?: string; // 工作单位
    remark?: string; // 备注
    idCard?: string; //身份证
    idCardType?: string; //
    cardType?: CardType; // 证件类型
    get cardTypeDisplay(): string {
        return "未定义";
    }

    // 理疗预约单独使用
    @JsonProperty({ type: PatientSource })
    patientSource?: PatientSource;

    // 患者tag列表
    @JsonProperty({ type: Array })
    tags?: PatientTag[];

    // 患者微信绑定状态
    wxBindStatus?: WxBindStatus;

    appFlag?: number;

    outPatientTimes?: number; //门诊次数
    payAmountTotal?: number; //累计消费
    payTimesTotal?: number; //付费次数

    profession?: string; //职业
    sn?: string; //档案号
    visitReason?: string; //到店原因

    pastHistory?: string; //
    allergicHistory?: string; //过敏史
    marital?: number; //婚姻
    weight?: number; //体重
    ethnicity?: string; //民族

    get maritalStr(): string {
        switch (this.marital) {
            case 4: {
                return "丧偶";
            }
            case 3: {
                return "离异";
            }
            case 2: {
                return "已婚";
            }
            case 1: {
                return "未婚";
            }
            case 0: {
                return "未知";
            }
            default: {
                return "";
            }
        }
    }

    arrearsFlag?: number; //是否有欠费

    get isWxBind(): boolean {
        return this.wxBindStatus == WxBindStatus.SUBSCRIBE_AND_BIND;
    }

    get canEditAge(): boolean {
        const { ValidatorUtils } = require("../../base-ui/utils/validator-utils");
        if (ValidatorUtils.isCardNo(this.idCard)) {
            return false;
        } else return !(!!this.idCard && !!this.age);
    }

    /**
     * 当前患者是否是客户端新建
     * 标识当前患者信息是否 存在于系统
     */
    get isHasPatient(): boolean {
        return !!this.id;
    }

    /**
     * 当前患者是否欠费
     */
    get hasArrearsOfFees(): boolean {
        return this.arrearsFlag === 1;
    }
}
export class PsychotropicNarcoticEmployee extends Patient {
    @JsonProperty({ type: Patient })
    patient?: Patient;
}

export class MemberType {
    id?: string;
    chainId?: string;
    name?: string;
    status?: number;
    createdBy?: string;
    created?: Date;
    lastModifiedBy?: string;
    lastModified?: Date;
}

export class Printable {
    chargeSheet?: boolean;
    dispensingSheet?: boolean;
    prescription?: boolean;
    executeInfusionSheet?: boolean;
    executeTreatmentSheet?: boolean;
    medicineTag?: boolean;
}

/**
 * discountBenefits: "西药3.0折, 中药3.0折, 中成药3.0折, 材料3.0折, 检验3.0折, 治疗3.0折, 理疗3.0折, 挂号3.0折"
 memberTypeId: "e1cfa333c2104024937a5fd1ae72f268"
 memberTypeName: "最强王者"
 memberTypeStatus: "1"
 patientId: "4a6a9a7695d149bda599a3ea7054a89d"
 */
class MemberTypeInfo {
    discountBenefits?: string;
    memberTypeId?: string;
    memberTypeName?: string;
    memberTypeStatus?: string;
}

export class MemberInfo {
    chainId?: string;
    patientId?: string;
    status?: number;
    memberTypeId?: string;
    principal?: number;
    present?: number;
    points?: number;
    pointsTotal?: number;
    remark?: string;
    created?: string;
    createdBy?: string;
    lastModifiedBy?: string;
    lastModified?: string;
    @JsonProperty({ type: Patient })
    patient?: Patient;
    @JsonProperty({ type: MemberType })
    memberType?: MemberType;
    @JsonProperty({ type: MemberTypeInfo })
    memberTypeInfo?: MemberTypeInfo;
    memberCardId?: string;
}

export class UsageInfo {
    ast?: number;
    days?: number;
    freq?: string;
    ivgtt?: number;
    usage?: string;
    dosage?: string;
    doseCount?: number;
    ivgttUnit?: string;
    dosageUnit?: string;
    usageLevel?: string;
    dailyDosage?: string;
    isDecoction?: boolean;
    requirement?: string; // 备注
    contactMobile?: string;
    specification?: string;
    executedTotalCount?: number; // 执行总数
    specialRequirement?: string;
    processUsage?: string; //e.g人工煎药
    usageType?: number; //加工类型
    usageSubType?: number; //加工子类型
    processBagUnitCount?: number; //每课袋数
    usageDays?: string; //服用天数
    processBagUnitCountDecimal?: number; //每剂袋数
    totalProcessCount?: number; //总袋数
    processRemark?: string; //加工备注

    //【feature】诊疗项目支持医生护士添加
    nurseId?: string;
    nurseName?: string;
    doctorId?: string;
    doctorName?: string;
    departmentId?: string;
    departmentName?: string;

    // 自定义字段，用于记录当前中药处方总剂数
    doseCount__?: number;

    externalUnitCount?: number;

    checked?: boolean;
}

export class DeliveryCompany {
    id?: string;
    name?: string;
    availablePayTypes?: DeliveryPayType[];
}

/// 快递费类型
export class DeliveryPayType {
    static freightCollect = 0; //到付
    static cashNow = 1; //寄付
}

class LogisticsTraceList {
    context?: string;
    ftime?: string;
}
export class PrescriptionFormLogisticsTraceRsp {
    tips?: string;
    @JsonProperty({ type: Array, clazz: LogisticsTraceList })
    traceList?: LogisticsTraceList[];
}

export class DeliveryInfo {
    id?: string;
    deliveryName?: string;
    addressDetail?: string;
    deliveryMobile?: string;
    addressCityId?: string;
    addressCityName?: string;
    addressDistrictId?: string;
    addressDistrictName?: string;
    patientId?: string;
    addressProvinceId?: string;
    chainId?: string;
    addressProvinceName?: string;

    chargeSheetId?: string;

    deliveryOrderNo?: string;
    deliveryCountryCode?: string;

    isAvailable?: number; //是否在有效配送范围
    @JsonProperty({ type: DeliveryCompany })
    deliveryCompany?: DeliveryCompany;

    // ignore: non_constant_identifier_names
    deliveryPayType?: number; //DeliveryPayType

    deliveryFee__?: number; //快递费
    expectDeliverFee__?: number; //在快递信息编辑页面，手动输入的快递费

    @JsonProperty({ clazz: PrescriptionFormLogisticsTraceRsp })
    __logisticTraceList?: PrescriptionFormLogisticsTraceRsp; //终端自定义物流信息

    deliveryNo?: string;

    displayAddress(delimiter = "/", includeDetail = true): string {
        const address: string[] = [];
        if (!_.isEmpty(this.addressProvinceName)) {
            address.push(this.addressProvinceName!);
        }
        if (!_.isEmpty(this.addressCityName)) {
            address.push(this.addressCityName!);
        }

        if (!_.isEmpty(this.addressDistrictName)) {
            address.push(this.addressDistrictName!);
        }

        return address.join(delimiter) + (includeDetail && !_.isEmpty(this.addressDetail) ? this.addressDetail : "");
    }
    // 当前处方最新物流信息
    get getLatestLogisticTrace(): string {
        const logisticTraceList = this.__logisticTraceList?.traceList;
        if (!logisticTraceList?.length) return "";
        return logisticTraceList?.[0]?.context ?? "";
    }

    get getCompanyNameAndDeliveryNo(): string {
        if (!!this.deliveryCompany?.name && !!this.deliveryNo) {
            return `${this.deliveryCompany?.name ?? ""}  ${this.deliveryNo ?? ""}`;
        }
        return "";
    }
}

export class Employee {
    id?: number;
    employeeId?: string;
    clinicId?: string;
    status?: number;
    employeeName?: string;
    employeeNamePy?: string;
    employeeNamePyFirst?: string;
    mobile?: string;
    headImgUrl?: string;
    roleId?: number;
    moduleIds?: string;
    moduleNames?: string;
    departmentNames?: string;
    selected?: boolean; // 终端自定义字段
    code?: number;
    isDoctor?: number;
    countryCode?: string;
    roles?: number[];
    handSign?: string;
    practiceInfo?: any;
    doctorIdCardNo?: string;
    nationalDoctorCode?: string;
}

export class ClinicDoctorInfo {
    clinicId?: string;
    regUnitPrice?: number;
    regCostUnitPrice?: number;
    unitPrice?: number;
    costUnitPrice?: number;
    isDefault?: number;
    departmentId?: string;
    departmentName?: string;
    doctorId?: string;
    doctorName?: string;
    namePy?: string;
    doctorNamePy?: string;
    namePyFirst?: string;
    doctorNamePyFirst?: string;
    headImgUrl?: string;
    practiceImgUrl?: string;

    revisitedRegCostUnitPrice?: number; //复诊挂号成本价

    revisitedRegUnitPrice?: number; //复诊挂号销售价

    departmentType?: DepartmentTypeStatus; // 科室类型
    handSign?: string; // 手写签名图片
    nationalDoctorCode?: string;
    mainMedicalName?: string; // 诊疗科目 （一级名称）
    secondMedicalName?: string; // 诊疗科目 （二级名称）

    //号种相关协议字段
    registrationCategory?: number; //挂号种类；0：普通门诊；1：专家门诊；2：便民门诊；
    registrationCategoryDisplay?(): string {
        if (this.registrationCategory == 2) {
            return "便民门诊";
        } else if (this.registrationCategory == 1) {
            return "专家门诊";
        } else {
            return "普通门诊";
        }
    }
}

export class AttachmentItem {
    uuid?: string;
    url?: string;
    signUrl?: string;
    useImageSignUrl?: boolean;
    fileName?: string;
    sort?: number;
    imageHeight?: number;
    imageWidth?: number;
    fileSize?: number;
}

export class DentistryMedicalRecordItem {
    toothNos?: ToothNos;
    @JsonProperty({ fromJson: fromBrToN })
    value?: string;
}

export class ObstetricalHistory {
    type?: string;
    birthCount?: number; //产
    pregnantCount?: number; // 孕
    menopauseAge?: string; //绝经年龄
    menopauseDate?: string; //末次月经日期
    menopauseTab?: number; //？？
    menophaniaAge?: number; //初潮年龄
    menstrualCycle?: number[]; //月经周期
    menstruationDays?: number[]; //行经天数
}

export class PositionsStrObj {
    static topLeft = "top-left";
    static topRight = "top-right";
    static bottomRight = "bottom-right";
    static bottomLeft = "bottom-left";
}
export class OralExaminationPos {
    dataNo?: string[];
    position?: string;
}
export class OralExaminationItem {
    describes?: string[];
    @JsonProperty({ type: Array, clazz: OralExaminationPos })
    positions?: OralExaminationPos[];
}

export class ExtendDiagnosisInfosItem {
    toothNos?: ToothNos;
    value?: DiseasesCode[];
}

export class EpidemiologicalListItem {
    label?: string;
    value?: string;
    isSuspicious?: boolean;
}

export class EpidemiologicalHistoryObj {
    attendantChecked?: boolean;
    patientChecked?: boolean;
    @JsonProperty({ type: Array, clazz: EpidemiologicalListItem })
    suspiciousList?: EpidemiologicalListItem[];
    @JsonProperty({ type: Array, clazz: EpidemiologicalListItem })
    symptomList?: EpidemiologicalListItem[];
}

export class EyeExaminationItem {
    key?: string;
    leftEyeValue?: string;
    name?: string;
    rightEyeValue?: string;
}
export class EyeExamination {
    @JsonProperty({ type: Array, clazz: EyeExaminationItem })
    items?: EyeExaminationItem[];
}
export enum ReportType {
    inspect = 1, //检验报告
    check = 2, //检查报告
}

export class ExamItems {
    chargeFormItemId?: string;
    chargeSheetId?: string;
    created?: string;
    examinationName?: string;
    examinationSheetId?: string;
    outpatientFormItemId?: string;
    patientOrderId?: string;
    status?: number;
    type?: ReportType; //报告类型
}

export function diagnosisFromJson(json: string[] | string | undefined): string {
    if (StringUtils.isJSON(json)) {
        //@ts-ignore
        json = JSON.parse(json);
    }
    if (_.isString(json)) {
        const reg2 = /[,，、;]*\d+\.\s*/g;

        let _arr = [];

        // 有拼接的 1.xxx 形式的诊断 按 1. 分割
        if (json.match(reg2)) {
            _arr = json.split(reg2);
        } else {
            _arr = [json];
        }
        return _arr.filter((item) => item).join(StringUtils.specialComma);
    }
    if (_.isArray(json)) {
        return json.join(StringUtils.specialComma);
    }
    return "";
}

export class InfectiousDiseases {
    classA?: string[];
    classB?: string[];
    classC?: string[];

    get hasInfectiousDiseases(): boolean {
        return !!(this.classA?.length || this.classB?.length || this.classC?.length);
    }
}

export class MedicalRecord {
    id?: string;
    qrid?: string;
    patientOrderId?: string;
    outpatientSheetId?: string;
    patientId?: string;
    clinicId?: string;
    chainId?: string;
    departmentId?: string;
    doctorId?: string;
    chiefComplaint?: string;
    pastHistory?: string;
    allergicHistory?: string; // 过敏史
    familyHistory?: string;
    epidemiologicalHistory?: string;
    personalHistory?: string;
    presentHistory?: string;
    physicalExamination?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    symptomTime?: Date;
    birthHistory?: string; // 出生史

    doctorAdvice?: string;
    chineseExamination?: string; //望闻切诊
    tongue?: string; //舌象
    pulse?: string; //脉象
    therapy?: string; //治法
    chinesePrescription?: string; // 方药
    syndrome?: string; //辨证
    syndromeTreatment?: string; //辨证论治
    @JsonProperty({ type: Array, clazz: DentistryMedicalRecordItem })
    auxiliaryExaminations?: DentistryMedicalRecordItem[]; //辅助检查
    get auxiliaryExamination(): string | undefined {
        return this.auxiliaryExaminations?.map((it) => it.value)?.join(StringUtils.specialComma);
    } //辅助检查

    /**
     * 病历字段汇总默认名
     */
    get medicalRecordDisplayName(): string {
        const _displayName: string[] = [];
        MedicalRecordUtils.MedicalRecordKey.map((item) => {
            let content = this?.[item.key];
            if ((this?.[item.key]?.length ?? 0) > 0) {
                if (item.key == "obstetricalHistoryDisplay") {
                    content = StringUtils.getObstetricalHistoryStr(this?.[item.key] ?? "");
                }
                if (item.key == "epidemiologicalHistory") {
                    content = StringUtils.stringBr2N(MedicalRecordUtils.getEpidemiologicalHistoryStr(this.__EpidemiologicalHistoryObj));
                }

                _displayName.push(`[${item.keyStr}] ${content}`);
            }
        });

        return _displayName.join();
    }

    @JsonProperty({
        fromJson: (str) => {
            if (!str) return [];
            if (str instanceof Object) {
                return str;
            }
            try {
                return JSON.parse(str);
            } catch (e) {
                return [];
            }
        },
    })
    obstetricalHistory?: (string | ObstetricalHistory)[]; //月经婚育史

    get obstetricalHistoryDisplay(): string {
        return MedicalRecordUtils.transObstetricalHistory(this.obstetricalHistory);
    }

    comment?: string; //备注

    wearGlassesHistory?: string; //戴镜史
    eyeExamination?: EyeExamination; //眼部检查
    @JsonProperty({ type: Array, clazz: ExamItems })
    examItems?: ExamItems[];

    @JsonProperty({ type: Array, clazz: AttachmentItem })
    attachments?: Array<AttachmentItem>;

    // 口腔检查
    @JsonProperty({ type: Array, clazz: DentistryMedicalRecordItem })
    dentistryExaminations?: DentistryMedicalRecordItem[];
    // 治疗计划
    @JsonProperty({ type: Array, clazz: DentistryMedicalRecordItem })
    treatmentPlans?: DentistryMedicalRecordItem[];
    // 处置
    @JsonProperty({ type: Array, clazz: DentistryMedicalRecordItem })
    disposals?: DentistryMedicalRecordItem[];

    // 诊断
    @JsonProperty({ type: Array, clazz: ExtendDiagnosisInfosItem })
    extendDiagnosisInfos?: ExtendDiagnosisInfosItem[];

    oralExamination?: string; //口腔检查

    // 附件图片
    @JsonProperty({ type: Array, clazz: AttachmentItem })
    preDiagnosisAttachments?: Array<AttachmentItem>;
    diagnosis?: string;

    get includeInfectiousDiseasesList(): InfectiousDiseases {
        const list = JsonMapper.deserialize(InfectiousDiseases, { classA: [], classB: [], classC: [] });
        const infectedDiseaseGrade = Object.keys(infectiousDiseases);
        infectedDiseaseGrade.forEach((item) => {
            // @ts-ignore
            infectiousDiseases[item]?.forEach((ele: string) => {
                const resDiagnosis = this.diagnosisInfos?.find((diagnosis) => diagnosis.name === ele);
                if (resDiagnosis) {
                    // @ts-ignore
                    list[item].push(resDiagnosis.name);
                }
            });
        });
        return list;
    }

    get diagnosisInfos(): DiseasesCode[] | undefined {
        let list: DiseasesCode[] = [];
        this.extendDiagnosisInfos?.map((it) => {
            list = list.concat(it.value ?? []);
        });
        return list;
    }

    set diagnosisInfos(list: DiseasesCode[] | undefined) {
        this.extendDiagnosisInfos = [{ toothNos: undefined, value: list }];
    }

    get showDentistryDiagnosisInfosText(): string {
        return (
            this.extendDiagnosisInfos
                ?.map((diagnosisInfoItem) => {
                    const toothNoDisplay = "";
                    // if (!!diagnosisInfoItem.toothNos?.length) {
                    //     toothNoDisplay = `${diagnosisInfoItem.toothNos.join("、")} `;
                    // }

                    return `${toothNoDisplay}${diagnosisInfoItem.value
                        ?.map((d_i) => d_i.name)
                        .filter((d_i) => !!d_i)
                        .join("、")}`;
                })
                .join(";") ?? ""
        );
    }

    get __EpidemiologicalHistoryObj(): EpidemiologicalHistoryObj | undefined {
        const epidemiologicalHistory = this.epidemiologicalHistory;
        if (epidemiologicalHistory) {
            let currentObj: EpidemiologicalHistoryObj = {
                patientChecked: false,
                attendantChecked: false,
                suspiciousList: [],
                symptomList: [],
            };

            try {
                const valObj = JSON.parse(epidemiologicalHistory);
                if (Array.isArray(valObj)) {
                    currentObj.symptomList = valObj;
                } else if (valObj && typeof valObj === "object") {
                    currentObj = _.assign(currentObj, valObj);
                }
            } catch (e) {
                // 如果是普通字符串，将其作为症状添加到 symptomList（pc支持手动输入，目前app不支持）
                currentObj.symptomList = [{ label: epidemiologicalHistory }];
            }
            return currentObj;
        }
        return undefined;
    }

    get displayEpidemiologicalHistory(): string {
        if (this.epidemiologicalHistory) {
            const currentList: { label: string; value: number }[] = JSON.parse(this.epidemiologicalHistory) ?? [];
            const _list = currentList.map((item) => {
                let str = item.label;
                if (item.value === 1) {
                    str += `（是）`;
                } else if (item.value === 0) {
                    str += `（否）`;
                }
                return str;
            });
            return _list.join("；");
        }
        return "";
    }

    get treatmentPlansStr(): string | undefined {
        return this.treatmentPlans?.map((it) => it.value)?.join(StringUtils.specialComma);
    }
    get disposalsStr(): string | undefined {
        return this.disposals?.map((it) => it.value)?.join(StringUtils.specialComma);
    }

    get dentistryExaminationsStr(): string | undefined {
        const strList = this.dentistryExaminations?.map((item) => {
            return !_.isEmpty(item?.toothNos) ? item?.toothNos?.join("、") : "" + !!item?.value ? " " + item?.value : "";
        });
        return strList?.join("");
    }

    get eyeExaminationStr(): string | undefined {
        if (_.isEmpty(this.eyeExamination?.items)) return "";
        const combineStrList: string[] = [];
        for (const item of this.eyeExamination?.items ?? []) {
            const structure = !!item.name ? item.name + "：" : "";
            const leftStructure = !!item.leftEyeValue ? "左眼（OS）" + item.leftEyeValue : "";
            const rightStructure = !!item.rightEyeValue ? "右眼（OD）" + item.rightEyeValue : "";
            if (!leftStructure && !rightStructure) continue;
            combineStrList.push(
                structure +
                    (!!rightStructure
                        ? rightStructure + (!!leftStructure ? "；" + leftStructure : "")
                        : !!leftStructure
                        ? leftStructure
                        : "")
            );
        }
        return StringUtils.htmlDecode(combineStrList.join("\n"));
    }

    static keyList: {
        key: keyof Pick<MedicalRecord, "chiefComplaint" | "presentHistory" | "allergicHistory" | "physicalExamination" | "diagnosis">;
        label: string;
        objKey?: keyof Pick<MedicalRecord, "extendDiagnosisInfos">;
    }[] = [
        { key: "chiefComplaint", label: "主诉" },
        { key: "presentHistory", label: "现病史" },
        { key: "physicalExamination", label: "体格检查" },
        { key: "diagnosis", label: "诊断", objKey: "extendDiagnosisInfos" },
    ];

    /**
     * 将主诉、现病史、体格检查、诊断合并到当前对象上
     * @param oldMedicalRecord
     */
    assignObj(oldMedicalRecord?: MedicalRecord): void {
        const _oldMedicalRecord = _.cloneDeep(oldMedicalRecord);
        MedicalRecord.keyList
            .filter((item) => {
                return item.key !== "diagnosis";
            })
            .map((item) => {
                const _key = item.key as keyof Pick<MedicalRecord, "chiefComplaint" | "presentHistory" | "physicalExamination">;
                if (!!this[_key]) {
                    if (!!_oldMedicalRecord![_key]) {
                        this[_key] = _oldMedicalRecord![_key] + "，" + this[_key];
                        //去重
                        const _list = this[_key]?.split("，") ?? [];
                        this[_key] = new Array(...new Set(_list)).join("，");
                    }
                }
            });
        if (!!_oldMedicalRecord?.extendDiagnosisInfos?.length) {
            const oldList: ExtendDiagnosisInfosItem[] = [];
            _oldMedicalRecord?.extendDiagnosisInfos.map((item) => {
                const diseasesCodeList: DiseasesCode[] = [];
                item.value?.map((it) => {
                    if (this.extendDiagnosisInfos?.every((r_item) => r_item.value?.every((r_it) => r_it.name != it.name))) {
                        diseasesCodeList.push(it);
                    }
                });
                if (!!diseasesCodeList.length) {
                    oldList.push({ toothNos: item.toothNos, value: diseasesCodeList });
                }
            });
            this.extendDiagnosisInfos = oldList.concat(this.extendDiagnosisInfos ?? []);
        }
    }
}

export class DecoctionProductInfo {
    id?: string;
    vendorId?: string;
    businessScopeId?: string;
    description?: string;
    type?: number;
    ladderInfo?: any;
    ruleInfo?: string;
    permanentPrice?: number;
}

/**
 * 空中药房辅料费
 */
export class IngredientProductInfo {
    id?: string;
    perUnitPrice?: number;
    perUnit?: number;
    type?: number;
    permanentCount?: number;
    perMatchingCount?: number;
    matchingPerUnitCount?: number;
    description?: string;
}

export interface IdWithName {
    id?: string;
    name?: string;
}

export interface GoodsInfoMaxCost {
    clinicName: string;
    packageCostPrice: number;
}

export class GoodsUnit {
    id?: string;
    name?: string;
    type?: number;
}

export class OnlineTreatmentUnits {
    customUnitList?: GoodsUnit[];
    maxCustomUnitCount?: number;
    sysUnitList?: GoodsUnit[];
    type?: number;
}

export enum FileType {
    doc = "doc",
    image = "image",
    pdf = "pdf",
    xls = "xls",
    ppt = "ppt",
}

interface strKeyObj {
    [key: string]: any;
}

export const fileTypeMap: strKeyObj = {
    image: ["jpg", "JPG", "png", "PNG", "gif", "GIF", "bmp", "BMP", "jpeg", "JPEG"],
    xls: ["xls", "XLS", "xlsx", "XLSX"],
    doc: ["doc", "DOC", "docx", "DOCX"],
    ppt: ["ppt", "PPT", "pptx", "PPTX"],
    pdf: ["pdf", "PDF"],
};

export interface FileBaseDetail {
    fileName: string;
    fileSize?: number;
    fileType: FileType;
    imageHeight?: number;
    imageWidth?: number;
    url: string;
}

export enum OssUpdateModules {
    CHRONIC_CARE = "chronic-care", // 慢病相关
    DOCTOR = "doctor", // 医生相关 ex头像 执业证照
    CALL_NUMBER = "call-number", // 叫号屏幕设置背景图
    SELF_SERVICE = "self-service", //自助服务机
    EXAMINATION = "examination", // 检查模块
    MEDICAL_RECORD = "medical-record", // 门诊病历模块
    PRESCRIPTION_SIGN = "prescription-sign", // 处方签名
    CHARGE_NOTIFICATION = "charge-notification", // 收费告知书
    EXECUTE_RECORD = "execute-record", //执行记录
    MARKETING = "marketing", // 营销-消息推送-短信签名资质
    BASIC = "basic", //门店基础信息：诊所 logo
    LICENSE = "license", //证照资质
    WEAPP = "weapp", //微诊所设置
    CRM = "crm", //crm 相关：随访
    IM = "im", //患者沟通/在线咨询
    WEAPP_2C = "weapp-2c", //微诊所 C 端用户上传
}

/**
 * 处方标记
 */
export enum PsychotropicNarcoticTypeEnum {
    // 精麻处方需要检查身份证
    NONE = 0, // 无
    JING1 = 1, // 精一
    JING2 = 2, // 精二
    MAZUI = 3, // 麻醉
    DU = 4, // 毒

    // 不需要检查身份证
    PUTONG = 5, // 普通
    ERKE = 6, // 儿科
    MANBING = 7, // 慢病
    LAONIANBING = 8, // 老年病
    JIZHEN = 9, // 急诊
    CHANGQI = 10, // 长期
}

export enum DepartmentTypeStatus {
    outPatientType = 1, // 门诊科室
    childHealthType = 2, // 儿保科室
}

export interface MedicinePharmacyInfo {
    name?: string; //药房名字
    no?: number; //药房号
    type?: number; //药房类型 0 本地药房 1 空中药房 2 虚拟药房
    typeName?: string; //药房类型的名称 0 本地药房 1 空中药房 2 虚拟药房
    pharmacyTag?: string; // 药房标签
}

export class SupervisionSccaDoctorCaInfoBody {
    doctorId?: string; //医生id
    status?: number; // 0正常 1注销
    cardNumber?: number; //证件号码
    caBeginDate?: Date; //证书申请日期
    caEndDate?: Date; //证书有效期至
    validDays?: string; //剩余有效天数
    certBase64?: string;
    certP7?: string;
    certSerialnumber?: string;

    /**
     * @description 是否已过期
     * @event true 已绑定
     * @event false 未绑定
     */
    get isExpired(): boolean {
        return (this.validDays ?? 0) <= 0;
    }
}
export class SupervisionSccaDoctorCaInfoRsp {
    body?: SupervisionSccaDoctorCaInfoBody;
    resultCode?: number; // 0 已绑定 1 已失效 undefined未绑定
    resultMsg?: string;
    success?: boolean;

    /**
     * @description 是否已绑定
     * @event true 已绑定
     * @event false 未绑定
     */
    get isAlreadyBound(): boolean {
        return this.resultCode === 0;
    }
    /**
     * @description 是否已失效
     * @event true 已绑定
     * @event false 未绑定
     */
    get isUnbound(): boolean {
        return this.resultCode === undefined;
    }
    /**
     * @description 是否未绑定
     * @event true 未绑定
     * @event false 已绑定 || 已失效
     */
    get isExpired(): boolean {
        return this.resultCode === 1;
    }
}
export enum HistoryPermissionModuleType {
    //挂号预约模块
    registration = "registration",
    //执行站模块
    execution = "execution",
    //门诊模块
    outpatient = "outpatient",
    //收费模块
    cashier = "cashier",
    //药房模块
    pharmacy = "pharmacy",
}

/**
 * 西药剂型
 */
export enum WesternMedicineDosageFormTypeEnum {
    PIAN = 100, // 片剂
    WAN = 200, // 丸剂
    SAN = 300, // 散剂
    RUANGAO = 400, // 软膏剂
    RUGAO = 401, // 乳膏剂
    KE = 500, // 颗粒剂
    JIAO = 600, // 胶囊剂
    RONG = 700, // 口服溶液剂
    HUN = 701, // 口服混悬剂
    RU = 702, // 口服乳剂
    ZHU = 800, // 注射剂
    WU = 900, // 气雾剂
    XI = 1000, // 洗剂
    CHA = 1100, // 搽剂
    HU = 1200, // 糊剂
    TIE = 1300, // 贴剂
    YAN = 1400, // 眼用制剂
    BI = 1401, // 鼻用制剂
    MO = 1500, // 膜剂
    SHUAN = 1600, // 栓剂
    TANG = 1700, // 糖浆剂
    HUAN = 1800, // 缓释制剂
    KONG = 1801, // 控释制剂
    NING = 1900, // 凝胶剂
    CHONG = 2000, // 冲洗剂
    TU = 2100, // 涂剂
    TUMO = 2200, // 涂膜剂
    DING = 2300, // 酊剂
    FEN = 2400, // 粉雾剂
    PEN = 2401, // 喷雾剂
    XIRU = 2500, // 吸入制剂
    ZHI = 2600, // 植入剂
    JIEYU = 2700, // 植入剂
    SHIFANG = 2800, // 宫内释放系统
    QI = 10000, // 其他
}

export const westernMedicineDosageFormTypeIds = [
    {
        type: WesternMedicineDosageFormTypeEnum.PIAN,
        label: "片剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.WAN,
        label: "丸剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.SAN,
        label: "散剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.RUANGAO,
        label: "软膏剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.RUGAO,
        label: "乳膏剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.KE,
        label: "颗粒剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.JIAO,
        label: "胶囊剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.RONG,
        label: "口服溶液剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.HUN,
        label: "口服混悬剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.RU,
        label: "口服乳剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.ZHU,
        label: "注射剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.WU,
        label: "气雾剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.XI,
        label: "洗剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.CHA,
        label: "搽剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.HU,
        label: "糊剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.TIE,
        label: "贴剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.YAN,
        label: "眼用制剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.BI,
        label: "鼻用制剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.MO,
        label: "膜剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.SHUAN,
        label: "栓剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.TANG,
        label: "糖浆剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.HUAN,
        label: "缓释制剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.KONG,
        label: "控释制剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.NING,
        label: "凝胶剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.CHONG,
        label: "冲洗剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.TU,
        label: "涂剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.TUMO,
        label: "涂膜剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.DING,
        label: "酊剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.FEN,
        label: "粉雾剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.PEN,
        label: "喷雾剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.XIRU,
        label: "吸入制剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.ZHI,
        label: "植入剂",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.JIEYU,
        label: "宫内节育系统",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.SHIFANG,
        label: "宫内释放系统",
    },
    {
        type: WesternMedicineDosageFormTypeEnum.QI,
        label: "其它",
    },
];

/**
 * 社保支付方式
 */
export enum SheBaoPayModeEnum {
    OVERALL = 0, // 优先统筹支付
    SELF = 1, // 优先个账支付
    NO_USE = 2, // 不使用医保支付
}

/**
 * 医疗器械分类 （一类医疗器械、二类医疗器械、三类医疗器械）
 */

export enum MedicalMaterialDeviceType {
    LEVEL_A = 1, // 一类医疗器械
    LEVEL_B = 2, // 二类医疗器械
    LEVEL_C = 3, // 三类医疗器械
}

/**
 * 基药类型：null 无意义 0 非基药 1 国家基药 2 地标基药
 */
export enum BaseMedicineTypeEnum {
    non = 0, //非基药
    national = 1, //国家基药
    landmarkBased = 2, //地标基药
}

/**
 * 毛利类型: 1 异常高毛利 2 A类 4 B类 8 C类 16 D类 32 E类
 */
export enum ProfitCategoryTypeEnum {
    high = 1, // 异常高毛利（PRICE > 120）
    leve_A = 2, // A类（90 ～ 120）
    leve_B = 4, // B类（60 ～ 90）
    leve_C = 8, // C类（30 ～ 60）
    leve_D = 16, // D类（0 ～ 30）
    leve_E = 32, // E类（PRICE < 0）
}

/**
 * 抗菌药物
 */
export enum AntibioticEnum {
    no = 0, // 非限制使用级
    yes = 1, // 限制使用级
    special = 2, // 特殊使用级
}

/**
 * 档案审批状态
 */
export enum GspModifyStatusEnum {
    waitVerify = 10, // 修改档案审批中
}

/***
 * 药品成分位/多选 0x01 精1 0x02 精2 0x04麻 0x08 毒 0x10放 0x20 麻黄碱
 * */
export enum IngredientEnum {
    JING_1 = 1 << 0, // 精1
    JING_2 = 1 << 1, // 精2
    MA = 1 << 2, // 麻
    DU = 1 << 3, // 毒
    FANG = 1 << 4, // 放
    MA_HUANG_JIAN = 1 << 5, // 麻黄碱
}

/**
 * OTC药物/非处方药: null 无含义  1 处方药 2 甲类非处方 4 乙类非处方
 */
export enum OtcTypeEnum {
    prescription = 1, // 处方药
    CLASS_A_OTC = 2, // 甲类非处方
    CLASS_B_OTC = 4, // 乙类非处方
}

/**
 * 药品养护类型 ：null无意义  1 无需养护 2 普通养护 4 重点养护
 * */
export enum MaintainTypeEnum {
    NO = 1, // 无需养护
    NORMAL = 2, // 普通养护
    VIP = 4, // 重点养护
}

/**
 * 存储类型 null无意义  0 常温 1 阴凉 2 冷藏 3 冷冻 4 避光
 */
export enum StorageTypeEnum {
    NORMAL = 1 << 0, // 常温:1
    COOL = 1 << 1, // 阴凉:2
    COLD = 1 << 2, // 冷藏:4
    FROZEN = 1 << 3, // 冷冻:8
    DARK = 1 << 4, // 避光16
    SEAL = 1 << 5, // 密封:32
}

/**
 * 定价类型（子店定价类型类型受配置控制，允许进价加成时可选）
 * 目前goods只支持一种定价类型
 * 为了扩展这里定义位
 */
export enum PriceTypeEnum {
    PRICE = 1, // 按进价加成
    PKG_PRICE_MAKEUP = 2, // 按固定售价
}

// 审核方式
export enum AuditTypeEnum {
    WECHAT = 1, // 微信
    CODE = 2, // 验证码
    PASSWORD = 3, // 密码
}

export class IntendedSurgeries {
    name?: string;
    code?: string;
    level?: string;
    site?: string;
    healingIncisionLevel?: string;
}

// 诊疗项目中手术类别申请单结构
export class SurgeryRequest {
    isInit?: boolean;
    name?: string;
    surgeryDepartmentId?: string; // 手术科室id
    surgeryDoctorId?: string; // 手术医生id
    assistantEmployeeIds?: string[]; // 手术助手id列表
    preoperativeDiagnosis?: string[]; // 术前诊断
    @JsonProperty({ type: Array, clazz: IntendedSurgeries })
    intendedSurgeries?: IntendedSurgeries[]; // 拟施手术
    type?: string; // 手术类型
    isDaytimeSurgery?: number; // 是否日间手术(患者在一个工作日内完成入院、手术和出院的一种手术模式)
    surgeryPosture?: string; // 手术体位
    estimateMinutes?: string; // 预计时长分钟数
    surgicalRequirements?: string; // 手术需求
    surgeryArrangement?: {
        surgeryDate?: string;
        operatingRoomId?: string;
        operatingRoomName?: string;
        anesthesiaDoctorId?: string;
        anesthesiaAssistantEmployeeIds?: string[]; // 麻醉助手
        surgeryNurseEmployeeIds?: string[]; // 手术护士
        circulatingNurseEmployeeIds?: string[]; // 巡回护士
        anesthesiaMethod?: string; // 麻醉方式
        asaLevel?: string; // asa分级
        preoperativeFastingFlag?: string; // 术前进食
    };
}
let _isDrugstoreButlerCallback: () => boolean = () => false;
export const setIsDrugstoreButlerCallback = (callback: () => boolean): (() => boolean) => {
    _isDrugstoreButlerCallback = callback;
    return _isDrugstoreButlerCallback;
};
