import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";
import logUtils from "../../common-base-module/log/log-utils";
import { UUIDGen } from "../../common-base-module/utils";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-05-2
 *
 * @description
 */

interface FileDownloaderResponse {
    statusCode: number;
    headers: Headers;
    filePath: string;
}

interface OnDownloadProgress {
    (currentBytes: number, totalBytes: number): void;
}

interface AbcRequestInit {
    /**
     * A BodyInit object or null to set request's body.
     */
    body?: BodyInit | null;
    /**
     * A string indicating how the request will interact with the browser's cache to set request's cache.
     */
    cache?: RequestCache;
    /**
     * A string indicating whether credentials will be sent with the request always, never, or only when sent to a same-origin URL. Sets request's credentials.
     */
    credentials?: RequestCredentials;
    /**
     * A Headers object, an object literal, or an array of two-item arrays to set request's headers.
     */
    headers?: HeadersInit;
    /**
     * A cryptographic hash of the resource to be fetched by request. Sets request's integrity.
     */
    integrity?: string;
    /**
     * A boolean to set request's keepalive.
     */
    keepalive?: boolean;
    /**
     * A string to set request's method.
     */
    method?: string;

    /**
     * 数据保存到指定文件
     */
    filePath: string;

    redirect: string;

    /**
     * 下载进度
     * @param currentBytes 当前下载到的位置
     * @param totalBytes 文件整个长度
     */
    onProgress?: OnDownloadProgress;
}

//文件下载
export class FileDownloader {
    /**
     * 下载文件
     * @param url 要下载的文件
     * @param init 参数
     */
    static downloadFile(url: string, init: AbcRequestInit): Promise<FileDownloaderResponse> {
        logUtils.d(`downloadFile url = ${url}, filePath = ${init.filePath}`);
        const { onProgress, method = "GET", ...otherInit } = init;
        const taskId = UUIDGen.generate(); //生成下载任务的唯一id

        const hippyEventEmitter = new HippyEventEmitter();
        const hippyEventEmitterHandler = hippyEventEmitter.addListener(
            "downloadFileCallback",
            (evt: { receivedBytes: number; totalBytes: number; taskId: string }) => {
                if (evt.taskId == taskId) {
                    onProgress?.(evt.receivedBytes, evt.totalBytes);
                }
            }
        );

        return callNativeWithPromise<string>("AbcNetwork", "downloadFile", {
            url: url,
            taskId: taskId,
            method: method,
            ...otherInit,
        })
            .then((result: string) => {
                try {
                    return JSON.parse(result);
                } catch (e) {
                    return result;
                }
            })
            .finally(() => {
                hippyEventEmitterHandler.remove();
            });
    }
}
