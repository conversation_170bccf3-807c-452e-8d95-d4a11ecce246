export default class URLProtocols {
    static ABC_SCHEME = "abcyun";
    static ABC_SCHEME_V2 = "www.abcyun.cn";

    static ABC_DEBUG_LOG = `${URLProtocols.ABC_SCHEME}://debug/log`;
    static ABC_UI_COMPONENTS = `${URLProtocols.ABC_SCHEME}://ui/components`;

    static OUTPATIENT_CONSULTATION = `${URLProtocols.ABC_SCHEME}://outpatient/consultation`;

    /// 打开门诊单
    static OUTPATIENT_SHEET = `${URLProtocols.ABC_SCHEME}://outpatient/outpatientSheet`;

    //打开网诊单
    static OUTPATIENT_NETSHEET = `${URLProtocols.ABC_SCHEME}://outpatient/netoutpatientSheet`;

    //打开指定网诊单，进行到接诊界面
    //abcyun://outpatient/consultation?conversationId=646382510699593728
    static OUTPATIENT_NET_OUTPATIENT = `${URLProtocols.ABC_SCHEME}://outpatient/consultation`;

    //打开快速接诊
    static OUTPATIENT_QUICKLY_ = `${URLProtocols.ABC_SCHEME}://outpatient/create`;

    //打开执行详情
    static EXECUTION_DETAIL = `${URLProtocols.ABC_SCHEME}://nursestation/execution_detail`;

    //收费相关
    static CHARGE_SHEET = `${URLProtocols.ABC_SCHEME}://charge/chargeSheet`;

    static OUTPATIENT_TAB = `${URLProtocols.ABC_SCHEME}://outpatient/tab`;
    static CHARGE_TAB = `${URLProtocols.ABC_SCHEME}://charge/tab`;
    static PHARMACY_TAB = `${URLProtocols.ABC_SCHEME}://pharmacy/tab`;

    //药房相关
    static DRUG_INVOICE = `${URLProtocols.ABC_SCHEME}://pharmacy/drug_invoice_detail`;

    //首页相关
    static ABCURL_HOME = `${URLProtocols.ABC_SCHEME}://home`;

    //登录页面
    static ABCURL_LOGIN = `${URLProtocols.ABC_SCHEME}://login`;

    //诊所切换
    static ABCURL_CLINIC_CHANGE = `${URLProtocols.ABC_SCHEME}://clinic/change`;

    static STAT = `${URLProtocols.ABC_SCHEME}://stat`;

    //打开主页，并定位到消息中心
    static MSG_PAGE = `${URLProtocols.ABC_SCHEME}://home/<USER>

    static INVENTORY_CHECK = `${URLProtocols.ABC_SCHEME}://inventory/check`;

    static PATIENT_REVISIT_DETAIL = `${URLProtocols.ABC_SCHEME}://patient/revisit`;

    static REGISTRATION_TAB = `${URLProtocols.ABC_SCHEME}://registration/tab`;

    static INVENTORY_DASHBOARD = `${URLProtocols.ABC_SCHEME}://inventory/tab`;

    //盘点单列表
    static INVENTORY_CHECK_LIST = `${URLProtocols.ABC_SCHEME}://inventory/_check/list`;

    //调拨单列表
    static INVENTORY_TRANS_LIST = `${URLProtocols.ABC_SCHEME}://inventory/trans/list`;

    /**
     * 调拨单详情
     * eg: abcyun://inventory/trans/detail?orderId=xxx
     */
    static INVENTORY_TRANS_DETAIL = `${URLProtocols.ABC_SCHEME}://inventory/trans/detail`;

    static UN_IMPLEMENT_PAGE = `${URLProtocols.ABC_SCHEME}://UnImplementPage`;

    // 盘点入库列表
    static INVENTORY_IN_LIST = `${URLProtocols.ABC_SCHEME}://inventory/in/list`;

    static INVENTORY_IN_DETAIL = `${URLProtocols.ABC_SCHEME}://inventory/in/detail`;

    // 盘点出库列表
    static INVENTORY_OUT_LIST = `${URLProtocols.ABC_SCHEME}://inventory/out/list`;

    // 盘点领用列表
    static INVENTORY_RECEPTION_LIST = `${URLProtocols.ABC_SCHEME}://inventory/reception/list`;

    //挂号详情
    static REGISTRATION_DETAIL = `${URLProtocols.ABC_SCHEME}://registration/detail`;

    //理疗详情
    static APPOINTMENT_DETAIL = `${URLProtocols.ABC_SCHEME}://appointment/detail`;

    // 复合统计列表
    static STAT_SUMMARY = `${URLProtocols.ABC_SCHEME}://stat/summary`;

    //业绩统计
    static STAT_PERFORMANCE = `${URLProtocols.ABC_SCHEME}://stat/performance`;

    //诊所基本经营统计
    static STAT_CLINIC_PERFORMANCE = `${URLProtocols.ABC_SCHEME}://stat/clinic/performance`;

    //订单云
    static ORDER_CLOUD = `${URLProtocols.ABC_SCHEME}://order/cloud`;

    // 清算单
    static SHEBAO_SETTLEMENT = `${URLProtocols.ABC_SCHEME}://shebao/settlement`;

    // 执行站TAB
    static EXECUTE_TAB = `${URLProtocols.ABC_SCHEME}://nurse-station/execute-tab-page`;

    //跳转到H5缴费页面
    static ABC_WEBVIEW = `${URLProtocols.ABC_SCHEME}://webview`;
    static ABC_WEBVIEW_V2 = `${URLProtocols.ABC_SCHEME_V2}://webview`;
    // 工作日报-库存预警
    static INVENTORY_WARN = `${URLProtocols.ABC_SCHEME}://inventory/warn`;
    //     系统通知跳转小程序
    static JUMP_LINK = `${URLProtocols.ABC_SCHEME}://jumpLink`;
    //     系统通知消息富文本
    static SYSTEM_NOTICE = `${URLProtocols.ABC_SCHEME}://cms`;

    // 语音录制
    static VOICE_MR = `${URLProtocols.ABC_SCHEME}://voiceMr`;
}
